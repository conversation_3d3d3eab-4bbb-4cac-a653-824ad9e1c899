{% extends "base.html" %}

{% block title %}Admin - System Settings{% endblock %}

{% block content %}
<div class="container">
    <div class="header">
        <h1>System Settings</h1>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
    </div>

    <div class="settings-container">
        <form method="POST" action="{{ url_for('admin_settings') }}">
            <div class="settings-section">
                <h2>General Settings</h2>
                <div class="form-group">
                    <label for="session_lifetime">Session Lifetime (days)</label>
                    <input type="number" id="session_lifetime" name="session_lifetime" 
                           value="{{ settings.session_lifetime }}" min="1" max="365">
                </div>
                <div class="form-group">
                    <label for="max_quiz_questions">Maximum Questions per Quiz</label>
                    <input type="number" id="max_quiz_questions" name="max_quiz_questions" 
                           value="{{ settings.max_quiz_questions }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="min_quiz_questions">Minimum Questions per Quiz</label>
                    <input type="number" id="min_quiz_questions" name="min_quiz_questions" 
                           value="{{ settings.min_quiz_questions }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="max_quiz_time">Maximum Quiz Time (minutes)</label>
                    <input type="number" id="max_quiz_time" name="max_quiz_time" 
                           value="{{ settings.max_quiz_time }}" min="1" max="180">
                </div>
            </div>

            <div class="settings-section">
                <h2>Email Settings</h2>
                <div class="form-group">
                    <label for="smtp_server">SMTP Server</label>
                    <input type="text" id="smtp_server" name="smtp_server" 
                           value="{{ settings.smtp_server }}">
                </div>
                <div class="form-group">
                    <label for="smtp_port">SMTP Port</label>
                    <input type="number" id="smtp_port" name="smtp_port" 
                           value="{{ settings.smtp_port }}">
                </div>
                <div class="form-group">
                    <label for="smtp_username">SMTP Username</label>
                    <input type="text" id="smtp_username" name="smtp_username" 
                           value="{{ settings.smtp_username }}">
                </div>
                <div class="form-group">
                    <label for="smtp_password">SMTP Password</label>
                    <input type="password" id="smtp_password" name="smtp_password" 
                           value="{{ settings.smtp_password }}">
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Save Settings</button>
            </div>
        </form>
    </div>
</div>

<style>
    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
    }

    .settings-container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        padding: 20px;
    }

    .settings-section {
        margin-bottom: 30px;
    }

    .settings-section h2 {
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #eee;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        color: #555;
        font-weight: 500;
    }

    .form-group input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .form-group input:focus {
        border-color: #007bff;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }

    .form-actions {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
        text-align: right;
    }

    .btn {
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: #007bff;
        color: white;
        border: none;
    }

    .btn-primary:hover {
        background: #0056b3;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
        text-decoration: none;
    }

    .btn-secondary:hover {
        background: #545b62;
    }
</style>
{% endblock %} 