<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome - Quiz Management System</title>
    <!-- Link to your existing stylesheet if needed, or keep styles here -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}"> -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Color System */
            --primary-color: #4A90E2;
            --primary-dark: #357ABD;
            --primary-light: #6BA4E7;
            --secondary-color: #2C3E50;
            --accent-color: #E74C3C;
            --text-primary: #2C3E50;
            --text-secondary: #7F8C8D;
            --background-light: #F5F7FA;
            --background-dark: #E4E9F2;
            --white: #FFFFFF;
            --error: #E74C3C;
            --success: #2ECC71;
            --warning: #F1C40F;
            
            /* Spacing System */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-xxl: 3rem;
            
            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 15px;
            --radius-full: 100px;
            
            /* Shadows */
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
            --shadow-lg: 0 5px 15px rgba(0,0,0,0.1);
        }

        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, var(--background-light) 0%, var(--background-dark) 100%);
            color: var(--text-primary);
            line-height: 1.6;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .container {
            width: 90%;
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 var(--spacing-md);
        }

        .hero {
            flex-grow: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: var(--spacing-xxl) 0;
        }

        .hero-content {
            background: var(--white);
            padding: var(--spacing-xxl);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            max-width: 600px;
        }

        .hero h2 {
            font-size: 2.8em;
            margin-bottom: var(--spacing-md);
            color: var(--secondary-color);
            font-weight: 700;
        }

        .hero p {
            font-size: 1.1em;
            margin-bottom: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-lg);
            flex-wrap: wrap;
        }

        /* Updated Button Style */
        .animated-button {
          position: relative;
            display: inline-flex;
          align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-md) var(--spacing-xl);
            border: 2px solid var(--primary-color);
            font-size: 1rem;
            background-color: var(--white);
            border-radius: var(--radius-full);
          font-weight: 600;
            color: var(--primary-color);
          cursor: pointer;
          overflow: hidden;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .animated-button:hover {
            background-color: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .animated-button:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }

        footer {
            text-align: center;
            padding: var(--spacing-md) 0;
            margin-top: auto;
            color: var(--text-secondary);
            font-size: 0.9em;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .hero h2 {
                font-size: 2.2em;
            }
            .hero p {
                font-size: 1em;
            }
            .animated-button {
                padding: var(--spacing-sm) var(--spacing-lg);
            }
        }

         @media (max-width: 480px) {
            .cta-buttons {
                flex-direction: column;
                align-items: center;
                gap: var(--spacing-md);
            }
            .animated-button {
                width: 80%;
            }
             .hero-content {
                padding: var(--spacing-xl);
             }
         }
    </style>
</head>
<body>
    {# Remove the duplicate header section #}
    {# <header>
        <div class="container">
            <h1>Quiz Platform</h1>
            <!-- Maybe add nav links here later if needed -->
        </div>
    </header> #}

    <main class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>Welcome to the Quiz Management System</h2>
                <p>Create, manage, and take quizzes with ease. Log in or sign up to get started!</p>
                <div class="cta-buttons">
                    <a href="{{ url_for('login') }}" class="animated-button">
                       <svg viewBox="0 0 24 24" class="arr-2" xmlns="http://www.w3.org/2000/svg">
                         <path
                           d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                         ></path>
                       </svg>
                       <span class="text">Login</span>
                       <span class="circle"></span>
                       <svg viewBox="0 0 24 24" class="arr-1" xmlns="http://www.w3.org/2000/svg">
                         <path
                           d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                         ></path>
                       </svg>
                     </a>
                     <a href="{{ url_for('signup') }}" class="animated-button">
                       <svg viewBox="0 0 24 24" class="arr-2" xmlns="http://www.w3.org/2000/svg">
                         <path
                           d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                         ></path>
                       </svg>
                       <span class="text">Sign Up</span>
                       <span class="circle"></span>
                       <svg viewBox="0 0 24 24" class="arr-1" xmlns="http://www.w3.org/2000/svg">
                         <path
                           d="M16.1716 10.9999L10.8076 5.63589L12.2218 4.22168L20 11.9999L12.2218 19.778L10.8076 18.3638L16.1716 12.9999H4V10.9999H16.1716Z"
                         ></path>
                       </svg>
                     </a>
                </div>
            </div>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Quiz Management System. All rights reserved.</p>
        </div>
    </footer>
</body>
</html>