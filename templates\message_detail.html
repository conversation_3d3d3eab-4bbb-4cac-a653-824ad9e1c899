{% extends "base.html" %}

{% block title %}Message{% if message.subject %}: {{ message.subject }}{% endif %}{% endblock %}

{% block content %}
<div class="container message-detail-container">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h1 class="mb-0">Message</h1>
        <a href="{{ url_for('inbox') }}" class="btn btn-sm btn-light">Back to Inbox</a>
    </div>

    <div class="card-body">
        <div class="message-meta">
            <p><strong>From:</strong> {{ message.sender.name }}</p>
            <p><strong>To:</strong> {{ message.receiver.name }}</p>
            <p><strong>Sent:</strong> {{ message.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <hr>
            <h5 class="message-subject">{{ message.subject | default('(No Subject)', true) }}</h5>
        </div>

        <div class="message-body-content">
            <!-- Correct way to handle newlines safely in Jinja -->
            {{ message.body | e | replace('\n', '<br>') | safe }}
        </div>

        <div class="message-detail-actions">
            {% if message.receiver_id == session['user_id'] %} 
                 <a href="{{ url_for('compose_message', to=message.sender.id, subject='Re: ' + message.subject if message.subject else 'Re: (No Subject)') }}" 
                    class="btn btn-primary">Reply</a>
            {% endif %}
            <!-- Optional: Add Delete Button here -->
            <!-- <button type="button" class="btn btn-danger">Delete</button> -->
        </div>
    </div>
</div>

<style>
/* Shared styles can go in base.css */
.message-detail-container {
    max-width: 850px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    overflow: hidden; /* Contain header background */
}

.card-header {
    background-color: #f8f9fa; /* Light header */
    color: #343a40;
    padding: 0.8rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
}

.card-header h1 {
    margin: 0;
    font-size: 1.4rem;
}

.card-body {
     padding: 1.5rem 2rem 2rem 2rem;
}

.message-meta {
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: #6c757d;
}
.message-meta p {
    margin-bottom: 0.2rem;
}
.message-meta p strong {
    display: inline-block;
    width: 55px; /* Align labels */
    color: #495057;
    font-weight: 600;
}
.message-meta hr {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-top: 1px solid #eee;
}
.message-subject {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0;
}

.message-body-content {
    padding: 1rem 0;
    line-height: 1.7;
    font-size: 1.05rem;
    color: #212529;
    white-space: pre-wrap; /* Respect whitespace and newlines */
    word-wrap: break-word;
}

.message-detail-actions {
    margin-top: 1.5rem;
    display: flex;
    gap: 0.8rem;
    justify-content: flex-start; /* Align actions left */
    border-top: 1px solid #eee;
    padding-top: 1.5rem;
}

/* Basic Button Styles */
.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    transition: all .15s ease-in-out;
}
.btn-sm {
     padding: 0.375rem 0.75rem;
     font-size: 0.875rem;
}
.btn-light {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #f8f9fa;
}
.btn-light:hover {
    color: #212529;
    background-color: #e2e6ea;
    border-color: #dae0e5;
}
.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}
.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}
.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}
.btn-danger:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Utility classes copied from Bootstrap */
.d-flex { display: flex; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }
.mb-0 { margin-bottom: 0; }

</style>
{% endblock %} 