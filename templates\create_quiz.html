{% extends "base.html" %}

{% block title %}Create New Quiz{% endblock %}

{% block content %}
<div class="container create-quiz-container">
    <h1>Create New Quiz</h1>
    <form method="post" action="{{ url_for('create_quiz') }}" id="create-quiz-form">
        <!-- Quiz Details Section -->
        <div class="form-section quiz-details-section">
            <h2>Quiz Details</h2>
            <div class="form-group">
                <label for="quiz_title">Quiz Title:</label>
                <input type="text" id="quiz_title" name="quiz_title" required>
            </div>
            <div class="form-group">
                <label for="quiz_description">Description (Optional):</label>
                <textarea id="quiz_description" name="quiz_description" rows="3"></textarea>
            </div>
             <div class="form-row">
                <div class="form-group half-width">
                    <label for="time_limit">Time Limit (Minutes):</label>
                    <input type="number" id="time_limit" name="time_limit" min="1" required>
                </div>
                 <div class="form-group half-width">
                    <label for="difficulty">Difficulty:</label>
                    <select id="difficulty" name="difficulty" required>
                        <option value="easy">Easy</option>
                        <option value="medium">Medium</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                 <label class="group-label">Grading Thresholds (%):</label>
                 <div class="form-row grading-row">
                     <div class="grade-input">
                         <label for="grade_a">A ≥</label>
                         <input type="number" id="grade_a" name="grade_a" min="0" max="100" value="90" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_b">B ≥</label>
                         <input type="number" id="grade_b" name="grade_b" min="0" max="100" value="80" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_c">C ≥</label>
                         <input type="number" id="grade_c" name="grade_c" min="0" max="100" value="70" required>
                     </div>
                     <div class="grade-input">
                         <label for="grade_d">D ≥</label>
                         <input type="number" id="grade_d" name="grade_d" min="0" max="100" value="60" required>
                     </div>
                 </div>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="form-section questions-section">
            <h2>Questions</h2>
            <div id="questions-container">
                <!-- Initial Question Template -->
                <div class="question-card" data-question-index="0">
                     <div class="question-card-header">
                        <span class="question-card-number">Question 1</span>
                        <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
                     </div>
                    <div class="form-group">
                        <textarea name="question[]" rows="2" required placeholder="Enter question text..."></textarea>
                        <!-- Hidden input for question type (always MCQ) -->
                        <input type="hidden" name="question_type[]" value="mcq">
                    </div>
                    <div class="form-row question-card-footer">
                        <!-- Type dropdown removed -->
                        <div class="form-group question-meta marks-only">
                            <label>Marks:</label>
                            <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
                        </div>
                    </div>

                    <!-- MCQ Fields -->
                    <div class="mcq-fields">
                        <label class="group-label">Options & Correct Answer:</label>
                        <div class="option-group">
                            <span class="option-marker">A</span>
                            <input type="text" name="option1[]" placeholder="Option 1" required>
                            <input type="radio" name="correct_answer[0]" value="1" required title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">B</span>
                            <input type="text" name="option2[]" placeholder="Option 2" required>
                            <input type="radio" name="correct_answer[0]" value="2" title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">C</span>
                            <input type="text" name="option3[]" placeholder="Option 3">
                             <input type="radio" name="correct_answer[0]" value="3" title="Mark as correct">
                        </div>
                        <div class="option-group">
                             <span class="option-marker">D</span>
                            <input type="text" name="option4[]" placeholder="Option 4">
                             <input type="radio" name="correct_answer[0]" value="4" title="Mark as correct">
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-outline" id="add-question-btn">+ Add Another Question</button>
        </div>
        
        <input type="hidden" id="total_marks" name="total_marks" value="1">
        <button type="submit" class="btn btn-primary btn-submit-quiz">Create Quiz</button>
    </form>
</div>

<script>
let questionIndex = 1;

function addQuestion() {
    const container = document.getElementById('questions-container');
    const newQuestionCard = document.createElement('div');
    newQuestionCard.classList.add('question-card');
    newQuestionCard.dataset.questionIndex = questionIndex;
    const currentQuestionNumber = questionIndex + 1;
    
    // Type dropdown removed, hidden input added
    newQuestionCard.innerHTML = `
        <div class="question-card-header">
            <span class="question-card-number">Question ${currentQuestionNumber}</span>
            <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
        </div>
        <div class="form-group">
            <textarea name="question[]" rows="2" required placeholder="Enter question text..."></textarea>
            <input type="hidden" name="question_type[]" value="mcq">
        </div>
        <div class="form-row question-card-footer">
            <div class="form-group question-meta marks-only">
                <label>Marks:</label>
                <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
            </div>
        </div>
        <div class="mcq-fields">
             <label class="group-label">Options & Correct Answer:</label>
             <div class="option-group">
                 <span class="option-marker">A</span>
                <input type="text" name="option1[]" placeholder="Option 1" required>
                <input type="radio" name="correct_answer[${questionIndex}]" value="1" required title="Mark as correct">
             </div>
             <div class="option-group">
                 <span class="option-marker">B</span>
                <input type="text" name="option2[]" placeholder="Option 2" required>
                 <input type="radio" name="correct_answer[${questionIndex}]" value="2" title="Mark as correct">
             </div>
            <div class="option-group">
                 <span class="option-marker">C</span>
                 <input type="text" name="option3[]" placeholder="Option 3">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="3" title="Mark as correct">
             </div>
             <div class="option-group">
                  <span class="option-marker">D</span>
                 <input type="text" name="option4[]" placeholder="Option 4">
                 <input type="radio" name="correct_answer[${questionIndex}]" value="4" title="Mark as correct">
             </div>
        </div>
    `;
    container.appendChild(newQuestionCard);
    questionIndex++;
    updateTotalMarks();
}

function removeQuestion(button) {
    const questionCard = button.closest('.question-card');
    questionCard.remove();
    const cards = document.querySelectorAll('#questions-container .question-card');
    cards.forEach((card, index) => {
        card.querySelector('.question-card-number').textContent = `Question ${index + 1}`;
        const radios = card.querySelectorAll('input[type="radio"]');
        radios.forEach(radio => {
             radio.name = `correct_answer[${index}]`;
        });
        card.dataset.questionIndex = index; 
    });
    questionIndex = cards.length; 
    updateTotalMarks();
}

function updateTotalMarks() {
    let total = 0;
    const marksInputs = document.querySelectorAll('.marks-input');
    marksInputs.forEach(input => {
        total += parseInt(input.value) || 0;
    });
    document.getElementById('total_marks').value = total;
}

document.getElementById('add-question-btn').addEventListener('click', addQuestion);

// Initial setup
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
    // No toggle needed anymore
});

</script>

<style>
/* Improved styling for create quiz page */
body {
    background-color: #f4f7f6; /* Lighter page background */
}

.create-quiz-container {
    max-width: 950px;
    margin: 2rem auto;
    background-color: transparent; /* Container is just for max-width */
    padding: 0; /* Remove padding from main container */
}

.create-quiz-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

/* Style for form sections (details, questions) */
.form-section {
    background-color: #fff;
    padding: 2rem 2.5rem;
    border-radius: 10px;
    margin-bottom: 2.5rem;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
}

.form-section h2 {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.8rem;
    margin-bottom: 2rem;
    color: #007bff; /* Theme color for headings */
    font-size: 1.6rem;
}

/* General form group styling */
.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.6rem;
    color: #495057;
    font-weight: 600; /* Slightly bolder labels */
    font-size: 0.95rem;
}

label.group-label {
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #343a40;
}

input[type="text"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    box-sizing: border-box;
    font-size: 1rem;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus,
select:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea {
    resize: vertical;
    min-height: 70px;
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem; /* Make space for arrow */
}

/* Layout helpers */
.form-row {
    display: flex;
    gap: 2rem;
    align-items: flex-end; /* Align items based on their bottom edge */
    margin-bottom: 1.5rem;
}

.form-group.half-width {
    flex: 1 1 50%; /* Allow shrinking but prefer 50% */
}

.grading-row {
    justify-content: space-around;
    gap: 1rem;
}

.grade-input {
     display: flex;
     flex-direction: column; /* Stack label and input */
     align-items: center;
     gap: 0.3rem;
     flex: 1;
     min-width: 60px; /* Prevent shrinking too much */
}
.grade-input label {
    margin-bottom: 0;
    white-space: nowrap;
    font-weight: normal;
    font-size: 0.9rem;
}
.grade-input input {
     width: 100%;
     text-align: center;
}

/* Question Card Styling */
.question-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 2rem;
    position: relative; 
    background-color: #fff;
    border-left: 4px solid #007bff; /* Accent border */
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.question-card-number {
    font-size: 1.2rem;
    font-weight: 600;
    color: #0056b3;
}

.remove-question-btn {
    background: #f1f3f5;
    color: #dc3545;
    border: 1px solid #dee2e6;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 1.4rem;
    font-weight: bold;
    line-height: 26px; 
    text-align: center;
    cursor: pointer;
    padding: 0;
    transition: background-color 0.2s, color 0.2s;
}
.remove-question-btn:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.question-card-footer {
    justify-content: flex-end; /* Push marks to the right */
}

.question-meta.marks-only {
   flex: 0 1 150px; /* Give marks input a reasonable max width */
   max-width: 150px; /* Adjust as needed */
}

/* MCQ Options Styling */
.mcq-fields {
    margin-top: 1.5rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    margin-bottom: 0.8rem;
    padding-left: 10px; /* Indent options slightly */
}

.option-marker {
    font-weight: bold;
    color: #6c757d;
    width: 20px; /* Fixed width for alignment */
    text-align: right;
}

.option-group input[type="text"] {
    flex-grow: 1;
}

.option-group input[type="radio"] {
    width: auto;
    margin-left: 10px;
    cursor: pointer;
    transform: scale(1.2); /* Make radio slightly larger */
}

/* Button Styling */
.btn {
    padding: 0.8rem 1.5rem;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    border: 1px solid transparent;
}

#add-question-btn {
    display: block;
    margin: 1.5rem auto 0 auto; /* Center add button */
}

.btn-primary {
    background-color: #28a745; /* Green for create */
    color: white;
    border-color: #28a745;
}
.btn-primary:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

.btn-outline {
    background-color: transparent;
    color: #007bff;
    border-color: #007bff;
}
.btn-outline:hover {
    background-color: #007bff;
    color: white;
}

.btn-submit-quiz {
    width: 100%;
    padding: 1rem;
    font-size: 1.2rem;
    margin-top: 2rem;
}

</style>
{% endblock %} 