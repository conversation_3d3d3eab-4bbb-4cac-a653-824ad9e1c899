{% extends "base.html" %}

{% block title %}Admin - Create Quiz{% endblock %}

{% block content %}
<div class="container admin-create-quiz-container">
    <div class="admin-header">
        <h1>Create New Quiz</h1>
        <a href="{{ url_for('admin_manage_quizzes') }}" class="btn btn-secondary">Back to Quiz Management</a>
    </div>

    <form method="post" action="{{ url_for('admin_create_quiz') }}" id="admin-create-quiz-form">
        <!-- Teacher Assignment Section -->
        <div class="form-section teacher-assignment-section">
            <h2>Teacher Assignment</h2>
            <div class="form-group">
                <label for="assigned_teacher_id">Assign to Teacher:</label>
                <select id="assigned_teacher_id" name="assigned_teacher_id" required>
                    <option value="">Select a teacher...</option>
                    {% for teacher in teachers %}
                        <option value="{{ teacher.id }}">{{ teacher.name }} ({{ teacher.email }})</option>
                    {% endfor %}
                </select>
            </div>
        </div>

        <!-- Quiz Details Section -->
        <div class="form-section quiz-details-section">
            <h2>Quiz Details</h2>
            <div class="form-group">
                <label for="quiz_title">Quiz Title:</label>
                <input type="text" id="quiz_title" name="quiz_title" required>
            </div>
            <div class="form-group">
                <label for="quiz_description">Description (Optional):</label>
                <textarea id="quiz_description" name="quiz_description" rows="3"></textarea>
            </div>
            <div class="form-row">
                <div class="form-group half-width">
                    <label for="time_limit">Time Limit (Minutes):</label>
                    <input type="number" id="time_limit" name="time_limit" min="1" max="180" required>
                </div>
                <div class="form-group half-width">
                    <label for="difficulty">Difficulty:</label>
                    <select id="difficulty" name="difficulty" required>
                        <option value="">Select difficulty...</option>
                        <option value="easy">Easy</option>
                        <option value="medium">Medium</option>
                        <option value="hard">Hard</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group quarter-width">
                    <label for="grade_a">Grade A Threshold (%):</label>
                    <input type="number" id="grade_a" name="grade_a" min="0" max="100" value="85" required>
                </div>
                <div class="form-group quarter-width">
                    <label for="grade_b">Grade B Threshold (%):</label>
                    <input type="number" id="grade_b" name="grade_b" min="0" max="100" value="75" required>
                </div>
                <div class="form-group quarter-width">
                    <label for="grade_c">Grade C Threshold (%):</label>
                    <input type="number" id="grade_c" name="grade_c" min="0" max="100" value="65" required>
                </div>
                <div class="form-group quarter-width">
                    <label for="grade_d">Grade D Threshold (%):</label>
                    <input type="number" id="grade_d" name="grade_d" min="0" max="100" value="50" required>
                </div>
            </div>
        </div>

        <!-- Questions Section -->
        <div class="form-section questions-section">
            <h2>Questions</h2>
            <div id="questions-container">
                <!-- Initial Question Template -->
                <div class="question-card" data-question-index="0">
                    <div class="question-card-header">
                        <span class="question-card-number">Question 1</span>
                        <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
                    </div>
                    <div class="form-group">
                        <textarea name="question[]" rows="2" required placeholder="Enter question text..."></textarea>
                        <input type="hidden" name="question_type[]" value="mcq">
                    </div>
                    <div class="form-row question-card-footer">
                        <div class="form-group question-meta marks-only">
                            <label>Marks:</label>
                            <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
                        </div>
                    </div>
                    <div class="mcq-fields">
                        <div class="option-group">
                            <span class="option-marker">A</span>
                            <input type="text" name="option1[]" placeholder="Option 1" required>
                            <input type="radio" name="correct_answer[0]" value="1" title="Mark as correct" required>
                        </div>
                        <div class="option-group">
                            <span class="option-marker">B</span>
                            <input type="text" name="option2[]" placeholder="Option 2" required>
                            <input type="radio" name="correct_answer[0]" value="2" title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">C</span>
                            <input type="text" name="option3[]" placeholder="Option 3">
                            <input type="radio" name="correct_answer[0]" value="3" title="Mark as correct">
                        </div>
                        <div class="option-group">
                            <span class="option-marker">D</span>
                            <input type="text" name="option4[]" placeholder="Option 4">
                            <input type="radio" name="correct_answer[0]" value="4" title="Mark as correct">
                        </div>
                    </div>
                </div>
            </div>
            <button type="button" class="btn btn-outline" id="add-question-btn">+ Add Another Question</button>
        </div>
        
        <input type="hidden" id="total_marks" name="total_marks" value="1">
        <div class="form-actions">
            <button type="submit" class="btn btn-primary btn-submit-quiz">Create Quiz</button>
            <a href="{{ url_for('admin_manage_quizzes') }}" class="btn btn-secondary">Cancel</a>
        </div>
    </form>
</div>

{% block styles %}
{{ super() }}
<style>
/* Import styles from create_quiz.html and add admin-specific styles */
.admin-create-quiz-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.admin-header h1 {
    color: #2c3e50;
    margin: 0;
}

.teacher-assignment-section {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.teacher-assignment-section h2 {
    color: #495057;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.form-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.form-section h2 {
    color: #495057;
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.9rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-row {
    display: grid;
    gap: 1rem;
    grid-template-columns: 1fr 1fr;
}

.form-row.quarter-width {
    grid-template-columns: repeat(4, 1fr);
}

.half-width {
    grid-column: span 1;
}

.quarter-width {
    grid-column: span 1;
}

.question-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.question-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.question-card-number {
    font-weight: 600;
    color: #495057;
}

.remove-question-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
}

.remove-question-btn:hover {
    background: #c82333;
}

.mcq-fields {
    margin-top: 1rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.option-marker {
    background: #007bff;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.option-group input[type="text"] {
    flex: 1;
    margin: 0;
}

.option-group input[type="radio"] {
    width: auto;
    margin: 0;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .admin-create-quiz-container {
        padding: 1rem;
    }
    
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
let questionIndex = 1;

function addQuestion() {
    const container = document.getElementById('questions-container');
    const newQuestionCard = document.createElement('div');
    newQuestionCard.className = 'question-card';
    newQuestionCard.setAttribute('data-question-index', questionIndex);
    
    const currentQuestionNumber = container.children.length + 1;
    
    newQuestionCard.innerHTML = `
        <div class="question-card-header">
            <span class="question-card-number">Question ${currentQuestionNumber}</span>
            <button type="button" class="remove-question-btn" onclick="removeQuestion(this)" title="Remove Question">×</button>
        </div>
        <div class="form-group">
            <textarea name="question[]" rows="2" required placeholder="Enter question text..."></textarea>
            <input type="hidden" name="question_type[]" value="mcq">
        </div>
        <div class="form-row question-card-footer">
            <div class="form-group question-meta marks-only">
                <label>Marks:</label>
                <input type="number" name="question_marks[]" min="1" value="1" required class="marks-input" onchange="updateTotalMarks()">
            </div>
        </div>
        <div class="mcq-fields">
            <div class="option-group">
                <span class="option-marker">A</span>
                <input type="text" name="option1[]" placeholder="Option 1" required>
                <input type="radio" name="correct_answer[${questionIndex}]" value="1" title="Mark as correct" required>
            </div>
            <div class="option-group">
                <span class="option-marker">B</span>
                <input type="text" name="option2[]" placeholder="Option 2" required>
                <input type="radio" name="correct_answer[${questionIndex}]" value="2" title="Mark as correct">
            </div>
            <div class="option-group">
                <span class="option-marker">C</span>
                <input type="text" name="option3[]" placeholder="Option 3">
                <input type="radio" name="correct_answer[${questionIndex}]" value="3" title="Mark as correct">
            </div>
            <div class="option-group">
                <span class="option-marker">D</span>
                <input type="text" name="option4[]" placeholder="Option 4">
                <input type="radio" name="correct_answer[${questionIndex}]" value="4" title="Mark as correct">
            </div>
        </div>
    `;
    
    container.appendChild(newQuestionCard);
    questionIndex++;
    updateTotalMarks();
}

function removeQuestion(button) {
    const questionCard = button.closest('.question-card');
    questionCard.remove();
    updateQuestionNumbers();
    updateTotalMarks();
}

function updateQuestionNumbers() {
    const questionCards = document.querySelectorAll('.question-card');
    questionCards.forEach((card, index) => {
        const numberSpan = card.querySelector('.question-card-number');
        numberSpan.textContent = `Question ${index + 1}`;
    });
}

function updateTotalMarks() {
    let total = 0;
    const marksInputs = document.querySelectorAll('.marks-input');
    marksInputs.forEach(input => {
        total += parseInt(input.value) || 0;
    });
    document.getElementById('total_marks').value = total;
}

document.getElementById('add-question-btn').addEventListener('click', addQuestion);

// Initial setup
document.addEventListener('DOMContentLoaded', () => {
    updateTotalMarks();
});
</script>
{% endblock %}
{% endblock %}
