import mysql.connector
from mysql.connector import <PERSON><PERSON><PERSON>
import os
from werkzeug.security import generate_password_hash

# Database configuration
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'bahrain@2008',
    'database': 'quiz_management'
}

def get_connection():
    """Create and return a database connection"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        if connection.is_connected():
            return connection
    except Error as e:
        print(f"Error connecting to MySQL database: {e}")
        return None

def create_database():
    """Create the database if it doesn't exist"""
    try:
        # Connect to MySQL server without specifying a database
        connection = mysql.connector.connect(
            host=DB_CONFIG['host'],
            user=DB_CONFIG['user'],
            password=DB_CONFIG['password']
        )

        if connection.is_connected():
            cursor = connection.cursor()

            # Create database if it doesn't exist
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {DB_CONFIG['database']}")
            print(f"Database '{DB_CONFIG['database']}' created or already exists.")

            cursor.close()
            connection.close()
            return True
    except Error as e:
        print(f"Error creating database: {e}")
        return False

def create_tables():
    """Create all necessary tables if they don't exist"""
    connection = get_connection()
    if not connection:
        return False

    cursor = connection.cursor()

    try:
        # Users table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            unhashed_password VARCHAR(255) NOT NULL,
            role VARCHAR(10) NOT NULL,
            parent_email VARCHAR(100),
            report_comment TEXT,
            is_verified BOOLEAN DEFAULT FALSE,
            verification_token VARCHAR(100)
        )
        """)

        # Quizzes table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS quizzes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(100) NOT NULL,
            description TEXT,
            time_limit INT NOT NULL,
            total_marks INT NOT NULL,
            grade_a_threshold INT NOT NULL,
            grade_b_threshold INT NOT NULL,
            grade_c_threshold INT NOT NULL,
            grade_d_threshold INT NOT NULL,
            difficulty VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            teacher_id INT,
            FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
        )
        """)

        # Questions table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS questions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            quiz_id INT NOT NULL,
            question_text VARCHAR(500) NOT NULL,
            question_type VARCHAR(20) NOT NULL,
            option1 VARCHAR(200),
            option2 VARCHAR(200),
            option3 VARCHAR(200),
            option4 VARCHAR(200),
            correct_answer VARCHAR(500) NOT NULL,
            marks INT NOT NULL,
            FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE
        )
        """)

        # Quiz Attempts table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS quiz_attempts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            quiz_id INT NOT NULL,
            score FLOAT NOT NULL,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (quiz_id) REFERENCES quizzes(id) ON DELETE CASCADE
        )
        """)

        # Quiz Answers table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS quiz_answers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            attempt_id INT NOT NULL,
            question_id INT NOT NULL,
            student_answer VARCHAR(500) NOT NULL,
            is_correct BOOLEAN NOT NULL,
            marks_awarded FLOAT NOT NULL,
            FOREIGN KEY (attempt_id) REFERENCES quiz_attempts(id) ON DELETE CASCADE,
            FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
        )
        """)

        # Messages table
        cursor.execute("""
        CREATE TABLE IF NOT EXISTS messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sender_id INT NOT NULL,
            receiver_id INT NOT NULL,
            subject VARCHAR(200),
            body TEXT NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_read BOOLEAN DEFAULT FALSE,
            FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
        )
        """)

        connection.commit()
        print("All tables created successfully.")
        return True
    except Error as e:
        print(f"Error creating tables: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def execute_query(query, params=None):
    """Execute a query with optional parameters"""
    connection = get_connection()
    if not connection:
        return False

    cursor = connection.cursor()
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        connection.commit()
        return True
    except Error as e:
        print(f"Error executing query: {e}")
        return False
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def fetch_one(query, params=None):
    """Execute a query and fetch one result"""
    connection = get_connection()
    if not connection:
        return None

    cursor = connection.cursor(dictionary=True)
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        result = cursor.fetchone()
        return result
    except Error as e:
        print(f"Error fetching data: {e}")
        return None
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()

def fetch_all(query, params=None):
    """Execute a query and fetch all results"""
    connection = get_connection()
    if not connection:
        return None

    cursor = connection.cursor(dictionary=True)
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        result = cursor.fetchall()
        return result
    except Error as e:
        print(f"Error fetching data: {e}")
        return None
    finally:
        if connection.is_connected():
            cursor.close()
            connection.close()
