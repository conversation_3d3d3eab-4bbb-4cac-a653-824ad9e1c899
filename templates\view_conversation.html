{% extends "base.html" %}

{% block title %}Chat with {{ other_user.name }}{% endblock %}

{% block content %}
<div class="container chat-view-container">
    <div class="chat-header">
        <h2>Chat with {{ other_user.name }} ({{ other_user.role.capitalize() }})</h2>
        <a href="{{ url_for('chat_list') }}" class="btn btn-sm btn-secondary">Back to Chats</a>
    </div>

    <div class="messages-window" id="messages-window">
        {% for message in messages %}
            <div class="message {% if message.sender_id == current_user_id %}sent{% else %}received{% endif %}">
                <div class="message-bubble">
                    <p class="message-content">{{ message.content }}</p>
                    <span class="message-timestamp">{{ message.timestamp.strftime('%H:%M') }}</span>
                </div>
            </div>
        {% else %}
            <p class="no-messages">No messages yet. Start the conversation!</p>
        {% endfor %}
    </div>

    <form class="message-form" method="post" action="{{ url_for('view_conversation', conversation_id=conversation.id) }}">
        <textarea name="message_content" placeholder="Type your message..." rows="3" required></textarea>
        <button type="submit" class="btn btn-primary">Send</button>
    </form>
</div>

<script>
    // Scroll to the bottom of the chat window on load
    const messagesWindow = document.getElementById('messages-window');
    messagesWindow.scrollTop = messagesWindow.scrollHeight;
</script>

<style>
.chat-view-container {
    max-width: 800px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    height: 75vh; /* Adjust height as needed */
}

.chat-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: #343a40;
}

.messages-window {
    flex-grow: 1; /* Takes up available space */
    padding: 1.5rem;
    overflow-y: auto; /* Enables scrolling */
    background-color: #f8f9fa; /* Slightly different background */
}

.message {
    margin-bottom: 1rem;
    display: flex;
}

.message.sent {
    justify-content: flex-end;
}

.message.received {
    justify-content: flex-start;
}

.message-bubble {
    max-width: 70%;
    padding: 0.7rem 1rem;
    border-radius: 15px;
    position: relative; /* For timestamp potentially */
}

.message.sent .message-bubble {
    background-color: #007bff;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.received .message-bubble {
    background-color: #e9ecef;
    color: #343a40;
    border-bottom-left-radius: 5px;
}

.message-content {
    margin: 0;
    word-wrap: break-word; /* Break long words */
    line-height: 1.4;
}

.message-timestamp {
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.4); /* Light timestamp for received */
    display: block;
    text-align: right;
    margin-top: 0.3rem;
}

.message.sent .message-timestamp {
     color: rgba(255, 255, 255, 0.7);
}

.no-messages {
    text-align: center;
    color: #6c757d;
    padding: 2rem;
}

.message-form {
    display: flex;
    padding: 1rem;
    border-top: 1px solid #e9ecef;
    background-color: #fff;
}

.message-form textarea {
    flex-grow: 1;
    resize: none;
    padding: 0.7rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 20px;
    margin-right: 0.5rem;
    font-size: 1rem;
    height: auto; /* Adjust height */
    min-height: 40px; /* Minimum height */
    max-height: 100px; /* Prevent excessive height */
}

.message-form textarea:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.message-form button {
    flex-shrink: 0;
    padding: 0.7rem 1.5rem;
    border-radius: 20px;
}

.btn-sm {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
}
</style>
{% endblock %} 