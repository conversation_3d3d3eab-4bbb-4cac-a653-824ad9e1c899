from flask import Flask, render_template, request, redirect, url_for, session, jsonify, flash, make_response
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from flask_mail import Mail, Message as MailMessage
import os
from datetime import datetime, timedelta
import re # Import regex module
import traceback # Import traceback module
from sqlalchemy import or_, and_
from functools import wraps
import random
import smtplib
import string
from itsdangerous import URLSafeTimedSerializer

app = Flask(__name__)
# Use a fixed secret key for session security
app.secret_key = 'your-secret-key-here'  # Replace with a secure secret key
# SQLite database configuration
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///quiz.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=7)  # Session lasts for 7 days

# Flask-Mail configuration
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # Replace with your Gmail address
app.config['MAIL_PASSWORD'] = 'rgxydmdmisnurhay'  # Replace with your 16-character app password
app.config['MAIL_DEFAULT_SENDER'] = '<EMAIL>'  # Replace with your Gmail address

mail = Mail(app)
db = SQLAlchemy(app)

# Add this after app configuration
app.config['SECURITY_PASSWORD_SALT'] = 'your-security-password-salt'  # Change this to a random string
serializer = URLSafeTimedSerializer(app.config['SECRET_KEY'])

# Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    email = db.Column(db.String(100), unique=True)
    password = db.Column(db.String(255))  # Hashed password
    unhashed_password = db.Column(db.String(255))  # Store unhashed password
    role = db.Column(db.String(10))  # admin, teacher, student, parent
    parent_email = db.Column(db.String(100), nullable=True)
    report_comment = db.Column(db.Text, nullable=True) # New field for teacher comments
    is_verified = db.Column(db.Boolean, default=False) # New field for admin verification
    verification_token = db.Column(db.String(100), nullable=True) # Token for verification

class Quiz(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    time_limit = db.Column(db.Integer, nullable=False)
    total_marks = db.Column(db.Integer, nullable=False)
    grade_a_threshold = db.Column(db.Integer, nullable=False)
    grade_b_threshold = db.Column(db.Integer, nullable=False)
    grade_c_threshold = db.Column(db.Integer, nullable=False)
    grade_d_threshold = db.Column(db.Integer, nullable=False)
    difficulty = db.Column(db.String(20), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    teacher_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    questions = db.relationship('Question', backref='quiz', lazy=True)
    teacher = db.relationship('User', backref='quizzes', lazy=True)

class Question(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'))
    question_text = db.Column(db.String(500))
    question_type = db.Column(db.String(20))  # mcq, true_false
    option1 = db.Column(db.String(200), nullable=True)
    option2 = db.Column(db.String(200), nullable=True)
    option3 = db.Column(db.String(200), nullable=True)
    option4 = db.Column(db.String(200), nullable=True)
    correct_answer = db.Column(db.String(500))  # For MCQ: option number, for true/false: true/false
    marks = db.Column(db.Integer)

class QuizAttempt(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    student_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    quiz_id = db.Column(db.Integer, db.ForeignKey('quiz.id'), nullable=False)
    score = db.Column(db.Float, nullable=False)
    submitted_at = db.Column(db.DateTime, default=datetime.utcnow)
    answers = db.relationship('QuizAnswer', backref='attempt', lazy=True)
    quiz = db.relationship('Quiz', backref='attempts', lazy=True)
    student = db.relationship('User', backref='quiz_attempts', lazy=True)

class QuizAnswer(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    attempt_id = db.Column(db.Integer, db.ForeignKey('quiz_attempt.id'), nullable=False)
    question_id = db.Column(db.Integer, db.ForeignKey('question.id'), nullable=False)
    selected_answer = db.Column(db.String(255), nullable=False)
    is_correct = db.Column(db.Boolean, nullable=False)

# --- NEW Email-Style Message Model ---
class Message(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    sender_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    receiver_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    subject = db.Column(db.String(200), nullable=True) # Optional subject
    body = db.Column(db.Text, nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    is_read = db.Column(db.Boolean, default=False, nullable=False)

    # Relationships to easily get sender/receiver user objects
    # Use backref to define User.sent_messages and User.received_messages
    sender = db.relationship('User', foreign_keys=[sender_id], backref=db.backref('sent_messages', lazy='dynamic'))
    receiver = db.relationship('User', foreign_keys=[receiver_id], backref=db.backref('received_messages', lazy='dynamic'))

# Initialize database
def init_db():
    with app.app_context():
        # Create tables if they don't exist
        db.create_all()

        # Create admin user if it doesn't exist
        if not User.query.filter_by(email='<EMAIL>').first():
            admin = User(
                name='Admin',
                email='<EMAIL>',
                password=generate_password_hash('admin123'),
                unhashed_password='admin123',  # Store unhashed version
                role='admin',
                is_verified=True  # Admin is verified by default
            )
            db.session.add(admin)
            db.session.commit()
            print("Admin user created successfully.")
        else:
            print("Admin user already exists.")

        # Add preloaded quizzes to all existing verified teachers
        add_preloaded_quizzes_to_all_teachers()

def create_preloaded_hard_quiz(teacher_id):
    """Create a preloaded hard quiz for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Advanced Mathematics Challenge"
    ).first()

    if existing_quiz:
        print(f"Math quiz already exists for teacher ID {teacher_id}")
        return existing_quiz

    # Create the quiz
    hard_quiz = Quiz(
        title="6th Grade Advanced Mathematics Challenge",
        description="A challenging quiz covering advanced 6th grade math concepts including algebra, geometry, and problem-solving.",
        teacher_id=teacher_id,
        time_limit=45,  # 45 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=85,
        grade_b_threshold=75,
        grade_c_threshold=65,
        grade_d_threshold=50,
        difficulty="hard"
    )
    db.session.add(hard_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 questions
    questions = [
        {
            "question_text": "If 2x + 3y = 12 and 3x - y = 4, what is the value of x + y?",
            "option1": "3",
            "option2": "4",
            "option3": "5",
            "option4": "6",
            "correct_answer": "2",  # option2 = 4
            "marks": 5
        },
        {
            "question_text": "A rectangular prism has a length of 8 cm, width of 5 cm, and height of 3 cm. What is its volume?",
            "option1": "40 cm³",
            "option2": "120 cm³",
            "option3": "80 cm³",
            "option4": "16 cm³",
            "correct_answer": "2",  # option2 = 120 cm³
            "marks": 5
        },
        {
            "question_text": "If a number is increased by 20% and then decreased by 25%, the final result is what percent of the original number?",
            "option1": "95%",
            "option2": "90%",
            "option3": "85%",
            "option4": "80%",
            "correct_answer": "2",  # option2 = 90%
            "marks": 5
        },
        {
            "question_text": "What is the next number in the sequence: 1, 4, 9, 16, 25, ...?",
            "option1": "30",
            "option2": "36",
            "option3": "49",
            "option4": "64",
            "correct_answer": "2",  # option2 = 36
            "marks": 5
        },
        {
            "question_text": "If the perimeter of a square is 36 units, what is its area?",
            "option1": "36 square units",
            "option2": "81 square units",
            "option3": "64 square units",
            "option4": "9 square units",
            "correct_answer": "2",  # option2 = 81 square units
            "marks": 5
        },
        {
            "question_text": "A train travels at 60 km/h for 2 hours and then at 80 km/h for 3 hours. What is the average speed for the entire journey?",
            "option1": "70 km/h",
            "option2": "72 km/h",
            "option3": "75 km/h",
            "option4": "68 km/h",
            "correct_answer": "2",  # option2 = 72 km/h
            "marks": 5
        },
        {
            "question_text": "If 3/4 of a number is 18, what is the number?",
            "option1": "13.5",
            "option2": "24",
            "option3": "27",
            "option4": "32",
            "correct_answer": "2",  # option2 = 24
            "marks": 5
        },
        {
            "question_text": "What is the value of x in the equation 2(x + 3) - 5 = 3x - 8?",
            "option1": "2",
            "option2": "3",
            "option3": "4",
            "option4": "5",
            "correct_answer": "2",  # option2 = 3
            "marks": 5
        },
        {
            "question_text": "A box contains 5 red marbles, 3 blue marbles, and 2 green marbles. If a marble is drawn at random, what is the probability of drawing a blue marble?",
            "option1": "1/10",
            "option2": "3/10",
            "option3": "1/5",
            "option4": "1/2",
            "correct_answer": "2",  # option2 = 3/10
            "marks": 5
        },
        {
            "question_text": "If the sum of interior angles of a polygon is 1080°, how many sides does the polygon have?",
            "option1": "6",
            "option2": "8",
            "option3": "10",
            "option4": "12",
            "correct_answer": "2",  # option2 = 8
            "marks": 5
        },
        {
            "question_text": "What is the least common multiple (LCM) of 12, 18, and 24?",
            "option1": "36",
            "option2": "72",
            "option3": "48",
            "option4": "144",
            "correct_answer": "2",  # option2 = 72
            "marks": 5
        },
        {
            "question_text": "If a circle has a diameter of 14 cm, what is its circumference? (Use π = 22/7)",
            "option1": "22 cm",
            "option2": "44 cm",
            "option3": "77 cm",
            "option4": "154 cm",
            "correct_answer": "2",  # option2 = 44 cm
            "marks": 5
        },
        {
            "question_text": "A car travels 240 miles on 10 gallons of gas. How many miles can it travel on 16 gallons of gas?",
            "option1": "256 miles",
            "option2": "384 miles",
            "option3": "400 miles",
            "option4": "480 miles",
            "correct_answer": "2",  # option2 = 384 miles
            "marks": 5
        },
        {
            "question_text": "If the ratio of boys to girls in a class is 3:5 and there are 24 boys, how many students are in the class?",
            "option1": "40",
            "option2": "64",
            "option3": "56",
            "option4": "80",
            "correct_answer": "2",  # option2 = 64
            "marks": 5
        },
        {
            "question_text": "What is the value of y in the equation 3y - 2(y + 4) = 5y - 14?",
            "option1": "-2",
            "option2": "-6",
            "option3": "6",
            "option4": "2",
            "correct_answer": "2",  # option2 = -6
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=hard_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Hard math quiz created successfully with 15 questions.")

    # Create a second hard quiz focused on geometry
    create_preloaded_geometry_quiz(teacher_id)

def create_preloaded_geometry_quiz(teacher_id):
    """Create a preloaded hard geometry quiz for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Advanced Geometry Challenge"
    ).first()

    if existing_quiz:
        print(f"Geometry quiz already exists for teacher ID {teacher_id}")
        # Create the fractions quiz after checking geometry quiz
        create_preloaded_fractions_quiz(teacher_id)
        return existing_quiz

    # Create the quiz
    geometry_quiz = Quiz(
        title="6th Grade Advanced Geometry Challenge",
        description="A challenging quiz focused on geometry concepts for 6th grade students, covering shapes, angles, area, volume, and spatial reasoning.",
        teacher_id=teacher_id,
        time_limit=45,  # 45 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=85,
        grade_b_threshold=75,
        grade_c_threshold=65,
        grade_d_threshold=50,
        difficulty="hard"
    )
    db.session.add(geometry_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 geometry questions
    questions = [
        {
            "question_text": "What is the area of a circle with radius 7 cm? (Use π = 22/7)",
            "option1": "44 cm²",
            "option2": "154 cm²",
            "option3": "22 cm²",
            "option4": "77 cm²",
            "correct_answer": "2",  # option2 = 154 cm²
            "marks": 5
        },
        {
            "question_text": "A rectangular prism has a volume of 360 cubic cm. If its length is 10 cm and its width is 6 cm, what is its height?",
            "option1": "6 cm",
            "option2": "36 cm",
            "option3": "60 cm",
            "option4": "216 cm",
            "correct_answer": "1",  # option1 = 6 cm
            "marks": 5
        },
        {
            "question_text": "What is the sum of the interior angles of a hexagon?",
            "option1": "360°",
            "option2": "540°",
            "option3": "720°",
            "option4": "1080°",
            "correct_answer": "3",  # option3 = 720°
            "marks": 5
        },
        {
            "question_text": "If a triangle has angles measuring 30° and 60°, what is the measure of the third angle?",
            "option1": "30°",
            "option2": "60°",
            "option3": "90°",
            "option4": "120°",
            "correct_answer": "3",  # option3 = 90°
            "marks": 5
        },
        {
            "question_text": "What is the perimeter of a regular octagon with sides of length 5 cm?",
            "option1": "35 cm",
            "option2": "40 cm",
            "option3": "45 cm",
            "option4": "50 cm",
            "correct_answer": "2",  # option2 = 40 cm
            "marks": 5
        },
        {
            "question_text": "A cube has a surface area of 96 square cm. What is the length of each edge?",
            "option1": "4 cm",
            "option2": "6 cm",
            "option3": "8 cm",
            "option4": "16 cm",
            "correct_answer": "1",  # option1 = 4 cm
            "marks": 5
        },
        {
            "question_text": "What is the area of a trapezoid with bases of 8 cm and 12 cm, and a height of 5 cm?",
            "option1": "40 cm²",
            "option2": "50 cm²",
            "option3": "60 cm²",
            "option4": "100 cm²",
            "correct_answer": "2",  # option2 = 50 cm²
            "marks": 5
        },
        {
            "question_text": "If a rectangle has a perimeter of 30 cm and a width of 5 cm, what is its length?",
            "option1": "5 cm",
            "option2": "10 cm",
            "option3": "15 cm",
            "option4": "20 cm",
            "correct_answer": "2",  # option2 = 10 cm
            "marks": 5
        },
        {
            "question_text": "What is the volume of a sphere with radius 3 cm? (Use π = 22/7 and V = 4/3πr³)",
            "option1": "36π cm³",
            "option2": "9π cm³",
            "option3": "12π cm³",
            "option4": "113.1 cm³",
            "correct_answer": "1",  # option1 = 36π cm³
            "marks": 5
        },
        {
            "question_text": "Two angles are complementary. If one angle is 37°, what is the measure of the other angle?",
            "option1": "43°",
            "option2": "53°",
            "option3": "63°",
            "option4": "143°",
            "correct_answer": "2",  # option2 = 53°
            "marks": 5
        },
        {
            "question_text": "What is the area of a rhombus with diagonals of 10 cm and 12 cm?",
            "option1": "60 cm²",
            "option2": "120 cm²",
            "option3": "22 cm²",
            "option4": "48 cm²",
            "correct_answer": "1",  # option1 = 60 cm²
            "marks": 5
        },
        {
            "question_text": "A cylinder has a radius of 5 cm and a height of 10 cm. What is its volume? (Use π = 3.14)",
            "option1": "157 cm³",
            "option2": "250 cm³",
            "option3": "785 cm³",
            "option4": "1570 cm³",
            "correct_answer": "3",  # option3 = 785 cm³
            "marks": 5
        },
        {
            "question_text": "What is the measure of each interior angle in a regular pentagon?",
            "option1": "72°",
            "option2": "108°",
            "option3": "120°",
            "option4": "135°",
            "correct_answer": "2",  # option2 = 108°
            "marks": 5
        },
        {
            "question_text": "If a triangle has sides of lengths 5 cm, 12 cm, and 13 cm, what type of triangle is it?",
            "option1": "Equilateral",
            "option2": "Isosceles",
            "option3": "Scalene",
            "option4": "Right-angled",
            "correct_answer": "4",  # option4 = Right-angled
            "marks": 5
        },
        {
            "question_text": "What is the total surface area of a rectangular prism with length 8 cm, width 4 cm, and height 3 cm?",
            "option1": "96 cm²",
            "option2": "108 cm²",
            "option3": "112 cm²",
            "option4": "136 cm²",
            "correct_answer": "3",  # option3 = 112 cm²
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=geometry_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Hard geometry quiz created successfully with 15 questions.")

    # Create the fractions quiz after creating geometry quiz
    create_preloaded_fractions_quiz(teacher_id)

def create_preloaded_fractions_quiz(teacher_id):
    """Create a preloaded medium difficulty quiz on fractions and decimals for 6th grade with 15 questions"""
    # Check if this quiz already exists for this teacher
    existing_quiz = Quiz.query.filter_by(
        teacher_id=teacher_id,
        title="6th Grade Fractions and Decimals"
    ).first()

    if existing_quiz:
        print(f"Fractions quiz already exists for teacher ID {teacher_id}")
        return existing_quiz

    # Create the quiz
    fractions_quiz = Quiz(
        title="6th Grade Fractions and Decimals",
        description="A medium difficulty quiz covering fractions, decimals, percentages, and related operations for 6th grade students.",
        teacher_id=teacher_id,
        time_limit=40,  # 40 minutes
        total_marks=75,  # 5 marks per question
        grade_a_threshold=80,
        grade_b_threshold=70,
        grade_c_threshold=60,
        grade_d_threshold=50,
        difficulty="medium"
    )
    db.session.add(fractions_quiz)
    db.session.flush()  # Get the quiz ID

    # Add 15 fractions and decimals questions
    questions = [
        {
            "question_text": "What is 2/5 + 3/10 in simplified form?",
            "option1": "1/2",
            "option2": "5/10",
            "option3": "7/10",
            "option4": "7/15",
            "correct_answer": "3",  # option3 = 7/10
            "marks": 5
        },
        {
            "question_text": "Convert 0.375 to a fraction in simplest form.",
            "option1": "3/8",
            "option2": "37.5/100",
            "option3": "3/80",
            "option4": "375/1000",
            "correct_answer": "1",  # option1 = 3/8
            "marks": 5
        },
        {
            "question_text": "What is 2.45 × 0.2?",
            "option1": "0.49",
            "option2": "0.245",
            "option3": "0.490",
            "option4": "4.9",
            "correct_answer": "3",  # option3 = 0.490
            "marks": 5
        },
        {
            "question_text": "Express 75% as a fraction in simplest form.",
            "option1": "75/100",
            "option2": "3/4",
            "option3": "7.5/10",
            "option4": "0.75/1",
            "correct_answer": "2",  # option2 = 3/4
            "marks": 5
        },
        {
            "question_text": "What is 3/4 ÷ 1/2?",
            "option1": "3/2",
            "option2": "6/4",
            "option3": "3/8",
            "option4": "1.5",
            "correct_answer": "1",  # option1 = 3/2
            "marks": 5
        },
        {
            "question_text": "Which decimal is equivalent to 5/8?",
            "option1": "0.58",
            "option2": "0.625",
            "option3": "0.825",
            "option4": "0.125",
            "correct_answer": "2",  # option2 = 0.625
            "marks": 5
        },
        {
            "question_text": "What is 1.75 - 0.8?",
            "option1": "0.95",
            "option2": "0.85",
            "option3": "1.05",
            "option4": "0.75",
            "correct_answer": "1",  # option1 = 0.95
            "marks": 5
        },
        {
            "question_text": "If 40% of a number is 60, what is the number?",
            "option1": "24",
            "option2": "150",
            "option3": "100",
            "option4": "240",
            "correct_answer": "2",  # option2 = 150
            "marks": 5
        },
        {
            "question_text": "What is 2/3 of 27?",
            "option1": "9",
            "option2": "18",
            "option3": "54",
            "option4": "81",
            "correct_answer": "2",  # option2 = 18
            "marks": 5
        },
        {
            "question_text": "Which fraction is greater: 5/8 or 7/12?",
            "option1": "5/8",
            "option2": "7/12",
            "option3": "They are equal",
            "option4": "Cannot be determined",
            "correct_answer": "1",  # option1 = 5/8
            "marks": 5
        },
        {
            "question_text": "What is 0.35 written as a percentage?",
            "option1": "3.5%",
            "option2": "35%",
            "option3": "0.35%",
            "option4": "350%",
            "correct_answer": "2",  # option2 = 35%
            "marks": 5
        },
        {
            "question_text": "What is the decimal equivalent of 7/20?",
            "option1": "0.35",
            "option2": "0.375",
            "option3": "0.7",
            "option4": "0.07",
            "correct_answer": "1",  # option1 = 0.35
            "marks": 5
        },
        {
            "question_text": "If a recipe calls for 3/4 cup of flour and you want to make 1/3 of the recipe, how much flour do you need?",
            "option1": "1/4 cup",
            "option2": "1/12 cup",
            "option3": "1/3 cup",
            "option4": "1/4 cup",
            "correct_answer": "1",  # option1 = 1/4 cup
            "marks": 5
        },
        {
            "question_text": "What is 3.75 rounded to the nearest tenth?",
            "option1": "3.7",
            "option2": "3.8",
            "option3": "4.0",
            "option4": "3.75",
            "correct_answer": "2",  # option2 = 3.8
            "marks": 5
        },
        {
            "question_text": "Which of the following is equivalent to 0.125?",
            "option1": "1/8",
            "option2": "1/4",
            "option3": "1/80",
            "option4": "12.5%",
            "correct_answer": "1",  # option1 = 1/8
            "marks": 5
        }
    ]

    for q_data in questions:
        question = Question(
            quiz_id=fractions_quiz.id,
            question_text=q_data["question_text"],
            question_type="mcq",
            option1=q_data["option1"],
            option2=q_data["option2"],
            option3=q_data["option3"],
            option4=q_data["option4"],
            correct_answer=q_data["correct_answer"],
            marks=q_data["marks"]
        )
        db.session.add(question)

    db.session.commit()
    print("Medium fractions quiz created successfully with 15 questions.")

def add_preloaded_quizzes_to_all_teachers():
    """Add preloaded quizzes to all existing verified teachers"""
    # Get all verified teachers
    teachers = User.query.filter_by(role='teacher', is_verified=True).all()

    if not teachers:
        print("No verified teachers found.")
        return

    print(f"Adding preloaded quizzes to {len(teachers)} verified teachers...")

    # Add quizzes to each teacher
    for teacher in teachers:
        # Check if the teacher already has quizzes
        existing_quizzes = Quiz.query.filter_by(teacher_id=teacher.id).count()

        if existing_quizzes == 0:
            # Teacher has no quizzes, add all preloaded quizzes
            create_preloaded_hard_quiz(teacher.id)
            print(f"Added preloaded quizzes to teacher {teacher.name} (ID: {teacher.id})")
        else:
            # Teacher has some quizzes, check which ones they're missing
            math_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Advanced Mathematics Challenge"
            ).first()

            geometry_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Advanced Geometry Challenge"
            ).first()

            fractions_quiz = Quiz.query.filter_by(
                teacher_id=teacher.id,
                title="6th Grade Fractions and Decimals"
            ).first()

            if not math_quiz:
                create_preloaded_hard_quiz(teacher.id)
                print(f"Added math quiz to teacher {teacher.name} (ID: {teacher.id})")
            elif not geometry_quiz:
                create_preloaded_geometry_quiz(teacher.id)
                print(f"Added geometry quiz to teacher {teacher.name} (ID: {teacher.id})")
            elif not fractions_quiz:
                create_preloaded_fractions_quiz(teacher.id)
                print(f"Added fractions quiz to teacher {teacher.name} (ID: {teacher.id})")
            else:
                print(f"Teacher {teacher.name} (ID: {teacher.id}) already has all preloaded quizzes")

# Call init_db when the application starts
init_db()

# Helper function for email validation
def is_valid_email(email):
    # Basic regex for email format
    return re.match(r"[^@]+@[^@]+\.[^@]+", email)

# Password validation helper
def is_strong_password(password):
    if len(password) < 8:
        return False
    if not any(c.islower() for c in password):
        return False
    if not any(c.isupper() for c in password):
        return False
    if not any(c.isdigit() for c in password):
        return False
    if not any(c in string.punctuation for c in password):
        return False
    return True

# Routes
@app.route('/')
def home():
    return render_template('home.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        email = request.form['email']
        password = request.form['password']
        user = User.query.filter_by(email=email).first()

        if user and check_password_hash(user.password, password):
            # Check if user is verified (except for admin users who are always verified)
            if not user.is_verified and user.role != 'admin':
                flash('Your account is pending verification by an administrator. Please wait for approval.', 'error')
                return redirect(url_for('login'))

            session['user_id'] = user.id
            session['user_role'] = user.role
            session['user_name'] = user.name

            # If this is a teacher, check if they have the preloaded quizzes
            if user.role == 'teacher':
                # Check if the teacher has any quizzes
                if not Quiz.query.filter_by(teacher_id=user.id).first():
                    # Add preloaded quizzes to this teacher
                    create_preloaded_hard_quiz(user.id)
                    print(f"Added preloaded quiz to teacher {user.name} on login")

            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid email or password. Please try again.', 'error')
            return redirect(url_for('login'))

    return render_template('login.html')

def generate_otp():
    """Generate a 6-digit OTP"""
    return str(random.randint(100000, 999999))

def send_otp_email(email, otp):
    msg = MailMessage(
        subject='Your Verification Code',
        recipients=[email]
    )
    msg.body = f'Your verification code is: {otp}\n\nThis code will expire in 10 minutes.'
    mail.send(msg)

def send_admin_verification_email(user):
    """Send an email to the admin when a new user registers"""
    admin_email = '<EMAIL>'  # Admin email address
    verification_url = url_for('verify_user_by_token', user_id=user.id, token=user.verification_token, _external=True)

    msg = MailMessage(
        subject='New User Registration Requires Verification',
        recipients=[admin_email]
    )

    msg.body = f'''
A new user has registered and requires verification:

Name: {user.name}
Email: {user.email}
Role: {user.role}
Parent Email: {user.parent_email if user.parent_email else 'N/A'}

To verify this user, please click the following link:
{verification_url}

To reject this user, visit the admin dashboard and manage pending users.
'''

    mail.send(msg)

@app.route('/signup', methods=['GET', 'POST'])
def signup():
    if request.method == 'POST':
        name = request.form['name'].strip()
        email = request.form['email'].strip().lower()
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        role = request.form['role']
        parent_email = request.form.get('parent_email', '').strip().lower()

        # --- Input Validation ---
        if password != confirm_password:
            flash('Passwords do not match. Please try again.', 'error')
            return redirect(url_for('signup'))

        # --- Password Strength Validation ---
        if not is_strong_password(password):
            flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
            return redirect(url_for('signup'))

        if not all([name, email, password, role]):
            flash('All fields except Parent Email (for non-students) are required.', 'error')
            return redirect(url_for('signup'))

        if not is_valid_email(email):
             flash('Invalid format for your email.', 'error')
             return redirect(url_for('signup'))

        # Role-specific email validation
        if role == 'student' and not email.endswith('@jpischool.com'):
            flash('Student email must end with @jpischool.com.', 'error')
            return redirect(url_for('signup'))
        elif role == 'teacher' and not email.endswith('@jpischool.com'):
            flash('Teacher email must end with @jpischool.com.', 'error')
            return redirect(url_for('signup'))
        elif role == 'parent' and not (email.endswith('@gmail.com') or email.endswith('@yahoo.com')):
            flash(f'Parent email must end with @gmail.com or @yahoo.com.', 'error')
            return redirect(url_for('signup'))

        # Parent email validation (mandatory for students)
        if role == 'student':
            if not parent_email:
                 flash('Parent email is required for student registration.', 'error')
                 return redirect(url_for('signup'))
            if not is_valid_email(parent_email):
                flash('Invalid format for parent email.', 'error')
                return redirect(url_for('signup'))
            if not (parent_email.endswith('@gmail.com') or parent_email.endswith('@yahoo.com')):
                flash('Parent email must end with @gmail.com or @yahoo.com.', 'error')
                return redirect(url_for('signup'))

            # Check if the provided parent email exists as a parent user
            parent_user = User.query.filter_by(email=parent_email, role='parent').first()
            if not parent_user:
                flash('The provided parent email is not registered as a parent account. Please ask your parent to sign up first.', 'error')
                return redirect(url_for('signup'))
        else:
             parent_email = None

        # --- Check for Existing User ---
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            flash('Email already exists. Please login or use a different email.', 'error')
            return redirect(url_for('signup'))

        # Generate OTP and store user data in session
        otp = generate_otp()
        session['signup_data'] = {
            'name': name,
            'email': email,
            'password': generate_password_hash(password),
            'unhashed_password': password,  # Store unhashed version
            'role': role,
            'parent_email': parent_email,
            'otp': otp,
            'otp_timestamp': datetime.now().timestamp()
        }

        # Send OTP email
        try:
            send_otp_email(email, otp)
            flash('Verification code has been sent to your email.', 'success')
            return redirect(url_for('verify_otp'))
        except Exception as e:
            print("Error sending OTP email:", e)
            import traceback; traceback.print_exc()
            flash('Failed to send verification code. Please try again.', 'error')
            return redirect(url_for('signup'))

    return render_template('signup.html')

@app.route('/verify-otp', methods=['GET', 'POST'])
def verify_otp():
    if 'signup_data' not in session:
        flash('Please complete the signup process first.', 'error')
        return redirect(url_for('signup'))

    if request.method == 'POST':
        entered_otp = request.form['otp']
        stored_data = session['signup_data']

        # Check if OTP is expired (10 minutes)
        if datetime.now().timestamp() - stored_data['otp_timestamp'] > 600:
            flash('Verification code has expired. Please request a new one.', 'error')
            return redirect(url_for('verify_otp'))

        if entered_otp == stored_data['otp']:
            # Generate a verification token
            verification_token = generate_reset_token(stored_data['email'])

            # Create new user with verification token and unverified status
            new_user = User(
                name=stored_data['name'],
                email=stored_data['email'],
                password=stored_data['password'],
                unhashed_password=stored_data['unhashed_password'],
                role=stored_data['role'],
                parent_email=stored_data['parent_email'],
                is_verified=False,
                verification_token=verification_token
            )

            try:
                db.session.add(new_user)
                db.session.commit()

                # If this is a teacher account, add preloaded quizzes
                if new_user.role == 'teacher':
                    create_preloaded_hard_quiz(new_user.id)
                    print(f"Added preloaded quiz to new teacher {new_user.name}")

                # Send verification email to admin
                try:
                    send_admin_verification_email(new_user)
                except Exception as mail_error:
                    print(f"Error sending admin verification email: {mail_error}")
                    # Continue even if email fails, as user is already created

                # Clear signup data from session
                session.pop('signup_data', None)
                flash('Account created successfully! Your account is pending verification by an administrator. You will be notified when your account is approved.', 'success')
                return redirect(url_for('login'))
            except Exception as e:
                db.session.rollback()
                print(f"Error creating user account: {e}")
                flash('An error occurred while creating your account. Please try again.', 'error')
                return redirect(url_for('signup'))
        else:
            flash('Invalid verification code. Please try again.', 'error')
            return redirect(url_for('verify_otp'))

    return render_template('verify_otp.html')

@app.route('/resend-otp')
def resend_otp():
    if 'signup_data' not in session:
        flash('Please complete the signup process first.', 'error')
        return redirect(url_for('signup'))

    # Generate new OTP
    otp = generate_otp()
    session['signup_data']['otp'] = otp
    session['signup_data']['otp_timestamp'] = datetime.now().timestamp()

    # Send new OTP email
    try:
        send_otp_email(session['signup_data']['email'], otp)
        flash('A new verification code has been sent to your email.', 'success')
    except Exception as e:
        flash('Failed to send verification code. Please try again.', 'error')

    return redirect(url_for('verify_otp'))

@app.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        flash('Please login to access the dashboard.', 'error')
        return redirect(url_for('login'))

    role = session['user_role']

    if role == 'admin':
        # Redirect admin users to the admin dashboard
        return redirect(url_for('admin_dashboard'))
    elif role == 'teacher':
        return redirect(url_for('teacher_dashboard'))
    elif role == 'student':
        return redirect(url_for('student_dashboard'))
    elif role == 'parent':
        return redirect(url_for('parent_dashboard'))
    else:
        flash('Unknown user role.', 'error')
        return redirect(url_for('login'))

@app.route('/logout')
def logout():
    session.clear()
    flash('You have been logged out successfully.', 'success')
    return redirect(url_for('home'))

@app.route('/teacher/dashboard')
def teacher_dashboard():
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))
    return render_template('teacher_dashboard.html')

@app.route('/teacher/create-quiz', methods=['GET', 'POST'])
def create_quiz():
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))

    if request.method == 'POST':
        print("--- Received POST request for /teacher/create-quiz ---")
        print(f"Form data: {request.form.to_dict(flat=False)}")
        try:
            # Create new quiz
            quiz = Quiz(
                title=request.form['quiz_title'],
                description=request.form.get('quiz_description', ''),
                teacher_id=session['user_id'],
                time_limit=request.form['time_limit'],
                total_marks=int(request.form['total_marks']),
                grade_a_threshold=int(request.form['grade_a']),
                grade_b_threshold=int(request.form['grade_b']),
                grade_c_threshold=int(request.form['grade_c']),
                grade_d_threshold=int(request.form['grade_d']),
                difficulty=request.form['difficulty']
            )
            db.session.add(quiz)
            db.session.flush() # Use flush to get the quiz ID before adding questions

            # Add questions (Only MCQ is expected now)
            question_texts = request.form.getlist('question[]')
            question_types = request.form.getlist('question_type[]') # Sent via hidden input, should be 'mcq'
            question_marks = request.form.getlist('question_marks[]')
            mcq_options1 = request.form.getlist('option1[]')
            mcq_options2 = request.form.getlist('option2[]')
            mcq_options3 = request.form.getlist('option3[]')
            mcq_options4 = request.form.getlist('option4[]')
            # Note: Correct answers are now indexed like correct_answer[0], correct_answer[1], etc.
            # We need to extract these carefully.
            correct_answer_map = {}
            for key, value in request.form.items():
                if key.startswith('correct_answer['):
                    index = int(key.split('[')[1].split(']')[0])
                    correct_answer_map[index] = value

            # Check if form data lengths match for mandatory fields
            num_questions = len(question_texts)
            if not (num_questions == len(question_types) == len(question_marks) == \
                      len(mcq_options1) == len(mcq_options2)):
                flash('Error: Form data length mismatch for core question fields.', 'error')
                db.session.rollback()
                return redirect(url_for('create_quiz'))

            if num_questions != len(correct_answer_map):
                 flash(f'Error: Mismatch between number of questions ({num_questions}) and selected correct answers ({len(correct_answer_map)}). Please ensure one answer is marked correct for each question.', 'error')
                 db.session.rollback()
                 return redirect(url_for('create_quiz'))


            for i in range(num_questions):
                # Check if the type is indeed MCQ (as expected from hidden input)
                if question_types[i] != 'mcq':
                    flash(f'Error: Internal error - Unexpected question type "{question_types[i]}" for question {i+1}.', 'error')
                    db.session.rollback()
                    return redirect(url_for('create_quiz'))

                # Get correct answer for this specific question index
                correct_ans_value = correct_answer_map.get(i)
                if correct_ans_value is None:
                    flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                    db.session.rollback()
                    return redirect(url_for('create_quiz'))

                question = Question(
                    quiz_id=quiz.id,
                    question_text=question_texts[i],
                    question_type='mcq',
                    marks=int(question_marks[i]),
                    option1=mcq_options1[i],
                    option2=mcq_options2[i],
                    option3=mcq_options3[i] if i < len(mcq_options3) else '', # Use empty string if not provided
                    option4=mcq_options4[i] if i < len(mcq_options4) else '',
                    correct_answer=correct_ans_value # Use the mapped value
                )
                db.session.add(question)

            db.session.commit()
            flash('Quiz created successfully!', 'success')
            return redirect(url_for('teacher_dashboard'))

        except Exception as e:
             db.session.rollback()
             error_details = traceback.format_exc()
             print(f"Error in create_quiz: {e}\n{error_details}")
             flash(f'An error occurred while creating the quiz: {e}', 'error') # Simplified user message
             return redirect(url_for('create_quiz'))

    return render_template('create_quiz.html')

@app.route('/my-quizzes')
def my_quizzes():
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))

    # Get all quizzes created by the current teacher
    quizzes = Quiz.query.filter_by(teacher_id=session['user_id']).all()
    return render_template('my_quizzes.html', quizzes=quizzes)

@app.route('/teacher/delete-quiz/<int:quiz_id>', methods=['DELETE'])
def delete_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return jsonify({'error': 'Unauthorized'}), 401

    quiz = Quiz.query.get_or_404(quiz_id)

    # Check if the quiz belongs to the current teacher
    if quiz.teacher_id != session['user_id']:
        return jsonify({'error': 'Unauthorized'}), 403

    # Delete all questions associated with the quiz
    Question.query.filter_by(quiz_id=quiz_id).delete()

    # Delete the quiz
    db.session.delete(quiz)
    db.session.commit()

    return jsonify({'message': 'Quiz deleted successfully'})

@app.route('/teacher/view-quiz/<int:quiz_id>')
def view_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))

    quiz = Quiz.query.get_or_404(quiz_id)

    # Check if the quiz belongs to the current teacher
    if quiz.teacher_id != session['user_id']:
        return redirect(url_for('my_quizzes'))

    # Get all questions for this quiz
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    return render_template('view_quiz.html', quiz=quiz, questions=questions)

@app.route('/teacher/edit-quiz/<int:quiz_id>', methods=['GET', 'POST'])
def edit_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'teacher':
        return redirect(url_for('login'))

    quiz = Quiz.query.get_or_404(quiz_id)

    if quiz.teacher_id != session['user_id']:
        flash('You are not authorized to edit this quiz.', 'error')
        return redirect(url_for('my_quizzes'))

    if request.method == 'POST':
        try:
            # Update quiz details
            quiz.title = request.form['quiz_title']
            quiz.description = request.form.get('quiz_description', '')
            quiz.time_limit = request.form['time_limit']
            quiz.total_marks = int(request.form['total_marks'])
            quiz.grade_a_threshold = int(request.form['grade_a'])
            quiz.grade_b_threshold = int(request.form['grade_b'])
            quiz.grade_c_threshold = int(request.form['grade_c'])
            quiz.grade_d_threshold = int(request.form['grade_d'])
            quiz.difficulty = request.form['difficulty']

            # Update questions (assuming only MCQs)
            question_texts = request.form.getlist('question[]')
            question_types = request.form.getlist('question_type[]') # Should be 'mcq' from hidden input
            question_marks = request.form.getlist('question_marks[]')
            mcq_options1 = request.form.getlist('option1[]')
            mcq_options2 = request.form.getlist('option2[]')
            mcq_options3 = request.form.getlist('option3[]')
            mcq_options4 = request.form.getlist('option4[]')

            # Parse correct answers from indexed radio buttons
            correct_answer_map = {}
            for key, value in request.form.items():
                if key.startswith('correct_answer['):
                    try:
                        index = int(key.split('[')[1].split(']')[0])
                        correct_answer_map[index] = value
                    except (IndexError, ValueError):
                        # Handle potential malformed key, though unlikely
                        flash(f'Warning: Malformed correct answer key received: {key}', 'warning')
                        continue # Skip this potentially problematic key

            # --- Validation ---
            num_questions = len(question_texts)
            if not (num_questions == len(question_types) == len(question_marks) == \
                    len(mcq_options1) == len(mcq_options2)):
                flash('Error: Form data length mismatch for core question fields.', 'error')
                # Don't commit, return to edit page with current values
                questions = Question.query.filter_by(quiz_id=quiz_id).all() # Reload for template
                return render_template('edit_quiz.html', quiz=quiz, questions=questions)

            if num_questions != len(correct_answer_map):
                flash(f'Error: Mismatch between questions ({num_questions}) and selected correct answers ({len(correct_answer_map)}). Mark one answer per question.', 'error')
                questions = Question.query.filter_by(quiz_id=quiz_id).all()
                return render_template('edit_quiz.html', quiz=quiz, questions=questions)

            # --- Update Database ---
            # Get existing questions
            existing_questions = Question.query.filter_by(quiz_id=quiz_id).all()
            existing_question_ids = [q.id for q in existing_questions]

            # Create a mapping to track old question IDs to new question IDs
            question_id_mapping = {}

            # First, update existing questions if possible
            for i in range(min(len(existing_questions), num_questions)):
                if question_types[i] != 'mcq': # Sanity check
                    flash(f'Error: Internal error - Unexpected question type "{question_types[i]}" for question {i+1}.', 'error')
                    db.session.rollback()
                    questions = Question.query.filter_by(quiz_id=quiz_id).all()
                    return render_template('edit_quiz.html', quiz=quiz, questions=questions)

                correct_ans_value = correct_answer_map.get(i)
                if correct_ans_value is None:
                    flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                    db.session.rollback()
                    questions = Question.query.filter_by(quiz_id=quiz_id).all()
                    return render_template('edit_quiz.html', quiz=quiz, questions=questions)

                # Update existing question
                existing_questions[i].question_text = question_texts[i]
                existing_questions[i].question_type = 'mcq'
                existing_questions[i].marks = int(question_marks[i])
                existing_questions[i].option1 = mcq_options1[i]
                existing_questions[i].option2 = mcq_options2[i]
                existing_questions[i].option3 = mcq_options3[i] if i < len(mcq_options3) else ''
                existing_questions[i].option4 = mcq_options4[i] if i < len(mcq_options4) else ''
                existing_questions[i].correct_answer = correct_ans_value

                # Keep track of the question ID (it stays the same)
                question_id_mapping[existing_questions[i].id] = existing_questions[i].id

            # If there are more new questions than existing ones, add them
            if num_questions > len(existing_questions):
                for i in range(len(existing_questions), num_questions):
                    if question_types[i] != 'mcq': # Sanity check
                        flash(f'Error: Internal error - Unexpected question type "{question_types[i]}" for question {i+1}.', 'error')
                        db.session.rollback()
                        questions = Question.query.filter_by(quiz_id=quiz_id).all()
                        return render_template('edit_quiz.html', quiz=quiz, questions=questions)

                    correct_ans_value = correct_answer_map.get(i)
                    if correct_ans_value is None:
                        flash(f'Error: No correct answer selected for question {i+1}.', 'error')
                        db.session.rollback()
                        questions = Question.query.filter_by(quiz_id=quiz_id).all()
                        return render_template('edit_quiz.html', quiz=quiz, questions=questions)

                    # Add new question
                    question = Question(
                        quiz_id=quiz.id,
                        question_text=question_texts[i],
                        question_type='mcq',
                        marks=int(question_marks[i]),
                        option1=mcq_options1[i],
                        option2=mcq_options2[i],
                        option3=mcq_options3[i] if i < len(mcq_options3) else '',
                        option4=mcq_options4[i] if i < len(mcq_options4) else '',
                        correct_answer=correct_ans_value
                    )
                    db.session.add(question)

            # If there are more existing questions than new ones, delete the extras
            if len(existing_questions) > num_questions:
                for i in range(num_questions, len(existing_questions)):
                    db.session.delete(existing_questions[i])

            db.session.commit()
            flash('Quiz updated successfully!', 'success')
            return redirect(url_for('my_quizzes'))

        except Exception as e:
            db.session.rollback()
            error_details = traceback.format_exc()
            print(f"Error in edit_quiz: {e}\n{error_details}")
            flash(f'An error occurred while updating the quiz: {e}', 'error')
            # Return to edit page with original data on error
            questions = Question.query.filter_by(quiz_id=quiz_id).all()
            return render_template('edit_quiz.html', quiz=quiz, questions=questions)

    # For GET request, show the edit form with existing data
    questions = Question.query.filter_by(quiz_id=quiz_id).all()
    return render_template('edit_quiz.html', quiz=quiz, questions=questions)

@app.route('/quiz/<int:quiz_id>/attempt', methods=['GET'])
def attempt_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Only students can attempt quizzes.', 'error')
        return redirect(url_for('dashboard'))

    quiz = Quiz.query.get_or_404(quiz_id)
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    # Check if student has already attempted this quiz
    existing_attempt = QuizAttempt.query.filter_by(
        student_id=session['user_id'],
        quiz_id=quiz_id
    ).first()

    if existing_attempt:
        flash('You have already attempted this quiz.', 'error')
        return redirect(url_for('student_dashboard'))

    return render_template('attempt_quiz.html', quiz=quiz, questions=questions)

@app.route('/quiz/<int:quiz_id>/submit', methods=['POST'])
def submit_quiz(quiz_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Only students can submit quizzes.', 'error')
        return redirect(url_for('dashboard'))

    quiz = Quiz.query.get_or_404(quiz_id)
    questions = Question.query.filter_by(quiz_id=quiz_id).all()

    # Check if student has already attempted this quiz
    existing_attempt = QuizAttempt.query.filter_by(
        student_id=session['user_id'],
        quiz_id=quiz_id
    ).first()

    if existing_attempt:
        flash('You have already attempted this quiz.', 'error')
        return redirect(url_for('student_dashboard'))

    # Calculate score
    total_marks = 0
    obtained_marks = 0

    # Create quiz attempt
    attempt = QuizAttempt(
        student_id=session['user_id'],
        quiz_id=quiz_id,
        score=0
    )
    db.session.add(attempt)
    db.session.flush()  # Get the attempt ID

    for question in questions:
        total_marks += question.marks
        selected_answer = request.form.get(f'question_{question.id}')

        if selected_answer:
            is_correct = False
            if question.question_type == 'mcq':
                # Convert both to strings for comparison
                is_correct = str(selected_answer).strip() == str(question.correct_answer).strip()
            elif question.question_type == 'true_false':
                # Convert both to lowercase strings for comparison
                is_correct = str(selected_answer).lower().strip() == str(question.correct_answer).lower().strip()

            if is_correct:
                obtained_marks += question.marks

            answer = QuizAnswer(
                attempt_id=attempt.id,
                question_id=question.id,
                selected_answer=selected_answer,
                is_correct=is_correct
            )
            db.session.add(answer)

    # Calculate final score
    score = (obtained_marks / total_marks) * 100 if total_marks > 0 else 0
    attempt.score = score

    db.session.commit()

    flash(f'Quiz submitted successfully! Your score: {score:.2f}%', 'success')
    return redirect(url_for('student_dashboard'))

def calculate_grade(score, quiz=None):
    """
    Calculate the grade based on the score and quiz thresholds.
    If quiz is provided, use its thresholds, otherwise use default thresholds.
    """
    if quiz:
        if score >= quiz.grade_a_threshold:
            return 'A'
        elif score >= quiz.grade_b_threshold:
            return 'B'
        elif score >= quiz.grade_c_threshold:
            return 'C'
        elif score >= quiz.grade_d_threshold:
            return 'D'
        else:
            return 'F'
    else:
        # Fallback to default thresholds if no quiz is provided
        if score >= 90:
            return 'A'
        elif score >= 80:
            return 'B'
        elif score >= 70:
            return 'C'
        elif score >= 60:
            return 'D'
        else:
            return 'F'

@app.route('/student/dashboard')
def student_dashboard():
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to access the dashboard.', 'error')
        return redirect(url_for('login'))

    # Get all available quizzes
    quizzes = Quiz.query.all()

    # Get past attempts with quiz data in chronological order (oldest first)
    past_attempts = QuizAttempt.query.filter_by(student_id=session['user_id'])\
        .join(Quiz)\
        .order_by(QuizAttempt.submitted_at.asc())\
        .all()

    # Calculate progress statistics
    total_attempts = len(past_attempts)
    average_score = sum(attempt.score for attempt in past_attempts) / total_attempts if total_attempts > 0 else 0

    # Prepare data for progress graph (now in chronological order)
    quiz_progress = {
        'labels': [f'Quiz {i+1}' for i in range(total_attempts)],
        'scores': [attempt.score for attempt in past_attempts]  # Will now be in chronological order
    }

    # Add grades to attempts
    for attempt in past_attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz)

    return render_template('student_dashboard.html',
                         quizzes=quizzes,
                         past_attempts=past_attempts,
                         total_attempts=total_attempts,
                         average_score=average_score,
                         quiz_progress=quiz_progress)

@app.route('/quiz/result/<int:attempt_id>')
def view_past_result(attempt_id):
    if 'user_id' not in session or session['user_role'] != 'student':
        flash('Please login as a student to view quiz results.', 'error')
        return redirect(url_for('login'))

    # Get the attempt with its associated quiz
    attempt = QuizAttempt.query\
        .join(Quiz)\
        .filter(QuizAttempt.id == attempt_id)\
        .first_or_404()

    # Verify the attempt belongs to the current student
    if attempt.student_id != session['user_id']:
        flash('You are not authorized to view this result.', 'error')
        return redirect(url_for('student_dashboard'))

    # Get all questions and answers for this attempt
    questions = Question.query.filter_by(quiz_id=attempt.quiz_id).all()
    answers = QuizAnswer.query.filter_by(attempt_id=attempt_id).all()

    # Create a dictionary of question_id to answer for easy lookup
    answer_dict = {answer.question_id: answer for answer in answers}

    # Get a list of all question IDs in the database
    current_question_ids = [q.id for q in questions]

    # Pair questions with their answers, including unanswered questions
    questions_with_answers = []

    # First, add all current questions with their answers
    for question in questions:
        answer = answer_dict.get(question.id)
        if answer:
            questions_with_answers.append((question, answer))
        else:
            # Create a dummy answer object for unanswered questions
            dummy_answer = QuizAnswer(
                attempt_id=attempt_id,
                question_id=question.id,
                selected_answer="",
                is_correct=False
            )
            dummy_answer.is_omitted = True  # Add a flag to identify omitted questions
            questions_with_answers.append((question, dummy_answer))

    # Now check for answers to questions that no longer exist in the quiz
    # (these would be from previous versions of the quiz)
    for answer in answers:
        if answer.question_id not in current_question_ids:
            # Try to get the question from the database (it might still exist)
            question = Question.query.get(answer.question_id)
            if question:
                # Question exists but is no longer part of this quiz
                questions_with_answers.append((question, answer))
            else:
                # Question no longer exists at all, create a placeholder
                placeholder_question = Question(
                    id=answer.question_id,
                    quiz_id=attempt.quiz_id,
                    question_text="[This question has been removed]",
                    question_type="mcq",
                    option1="[Option no longer available]",
                    option2="[Option no longer available]",
                    option3="[Option no longer available]",
                    option4="[Option no longer available]",
                    correct_answer="",
                    marks=0
                )
                questions_with_answers.append((placeholder_question, answer))

    # Calculate correct, incorrect, and omitted counts
    correct_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and answer.is_correct)
    incorrect_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and not answer.is_correct)
    omitted_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') and answer.is_omitted)

    # Calculate time taken (in minutes) - fix the calculation to use a more reasonable time
    # Instead of quiz.created_at (which is when the quiz was created by the teacher),
    # we should ideally track when the student started the quiz
    # For now, we'll estimate based on the time limit of the quiz
    time_taken = min((attempt.submitted_at - attempt.quiz.created_at).total_seconds() / 60,
                     float(attempt.quiz.time_limit)) if attempt.quiz.created_at else 0

    return render_template('past_quiz_result.html',
                         quiz=attempt.quiz,
                         attempt=attempt,
                         questions_with_answers=questions_with_answers,
                         correct_count=correct_count,
                         incorrect_count=incorrect_count,
                         omitted_count=omitted_count,
                         total_questions=len(questions_with_answers),
                         time_taken=f"{time_taken:.1f} minutes",
                         grade=calculate_grade(attempt.score, attempt.quiz))

@app.route('/parent/dashboard')
def parent_dashboard():
    if 'user_id' not in session or session['user_role'] != 'parent':
        flash('Please login as a parent to access the dashboard.', 'error')
        return redirect(url_for('login'))

    parent_email = User.query.get(session['user_id']).email

    # Find all children linked to this parent
    children = User.query.filter_by(role='student', parent_email=parent_email).all()

    children_data = []
    for child in children:
        # Get attempts for each child, ordered chronologically
        attempts = QuizAttempt.query.filter_by(student_id=child.id)\
            .join(Quiz)\
            .order_by(QuizAttempt.submitted_at.asc())\
            .all()

        # Add grades to attempts
        for attempt in attempts:
            attempt.grade = calculate_grade(attempt.score, attempt.quiz)

        children_data.append({
            'id': child.id,
            'name': child.name,
            'attempts': attempts
        })

    return render_template('parent_dashboard.html', children_data=children_data)

# Route for parents to view a specific child's quiz result
@app.route('/parent/result/<int:attempt_id>')
def view_child_result(attempt_id):
    if 'user_id' not in session or session['user_role'] != 'parent':
        flash('Please login as a parent to view results.', 'error')
        return redirect(url_for('login'))

    attempt = QuizAttempt.query.get_or_404(attempt_id)
    child = User.query.get(attempt.student_id)
    parent_email = User.query.get(session['user_id']).email

    # Verify this child is linked to the logged-in parent
    if child.parent_email != parent_email:
        flash('You are not authorized to view this result.', 'error')
        return redirect(url_for('parent_dashboard'))

    # Reuse the student's result view logic
    questions = Question.query.filter_by(quiz_id=attempt.quiz_id).all()
    answers = QuizAnswer.query.filter_by(attempt_id=attempt_id).all()
    answer_dict = {answer.question_id: answer for answer in answers}

    # Get a list of all question IDs in the database
    current_question_ids = [q.id for q in questions]

    # Pair questions with their answers, including unanswered questions
    questions_with_answers = []

    # First, add all current questions with their answers
    for question in questions:
        answer = answer_dict.get(question.id)
        if answer:
            questions_with_answers.append((question, answer))
        else:
            # Create a dummy answer object for unanswered questions
            dummy_answer = QuizAnswer(
                attempt_id=attempt_id,
                question_id=question.id,
                selected_answer="",
                is_correct=False
            )
            dummy_answer.is_omitted = True  # Add a flag to identify omitted questions
            questions_with_answers.append((question, dummy_answer))

    # Now check for answers to questions that no longer exist in the quiz
    # (these would be from previous versions of the quiz)
    for answer in answers:
        if answer.question_id not in current_question_ids:
            # Try to get the question from the database (it might still exist)
            question = Question.query.get(answer.question_id)
            if question:
                # Question exists but is no longer part of this quiz
                questions_with_answers.append((question, answer))
            else:
                # Question no longer exists at all, create a placeholder
                placeholder_question = Question(
                    id=answer.question_id,
                    quiz_id=attempt.quiz_id,
                    question_text="[This question has been removed]",
                    question_type="mcq",
                    option1="[Option no longer available]",
                    option2="[Option no longer available]",
                    option3="[Option no longer available]",
                    option4="[Option no longer available]",
                    correct_answer="",
                    marks=0
                )
                questions_with_answers.append((placeholder_question, answer))

    # Calculate correct, incorrect, and omitted counts
    correct_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and answer.is_correct)
    incorrect_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') == False and not answer.is_correct)
    omitted_count = sum(1 for _, answer in questions_with_answers if hasattr(answer, 'is_omitted') and answer.is_omitted)

    # Calculate time taken (in minutes) - fix the calculation to use a more reasonable time
    time_taken = min((attempt.submitted_at - attempt.quiz.created_at).total_seconds() / 60,
                     float(attempt.quiz.time_limit)) if attempt.quiz.created_at else 0

    # Render the same past_quiz_result template, but maybe add a parent context if needed
    return render_template('past_quiz_result.html',
                         quiz=attempt.quiz,
                         attempt=attempt,
                         questions_with_answers=questions_with_answers,
                         correct_count=correct_count,
                         incorrect_count=incorrect_count,
                         omitted_count=omitted_count,
                         total_questions=len(questions_with_answers),
                         time_taken=f"{time_taken:.1f} minutes",
                         grade=calculate_grade(attempt.score, attempt.quiz))

# --- NEW Messaging Routes ---

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/compose', methods=['GET', 'POST'])
@login_required
def compose_message():
    user_id = session['user_id']
    # Pre-fill recipient if ID is provided (e.g., from a reply button)
    recipient_id = request.args.get('to')
    recipient = User.query.get(recipient_id) if recipient_id else None
    subject = request.args.get('subject', '') # Pre-fill subject for replies

    if request.method == 'POST':
        receiver_username = request.form.get('receiver_username', '').strip()
        subject_form = request.form.get('subject', '').strip()
        body = request.form.get('body', '').strip()

        if not receiver_username or not body:
            flash('Receiver username and message body are required.', 'error')
            # Re-render form with entered data
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        receiver = User.query.filter_by(name=receiver_username).first()
        if not receiver:
            flash(f'Recipient with username "{receiver_username}" not found.', 'error')
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        if receiver.id == user_id:
             flash('You cannot send a message to yourself.', 'error')
             return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

        try:
            msg = Message(
                sender_id=user_id,
                receiver_id=receiver.id,
                subject=subject_form,
                body=body
            )
            db.session.add(msg)
            db.session.commit()
            flash('Message sent successfully!', 'success')
            return redirect(url_for('inbox'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error sending message: {e}', 'error')
            # Log the error
            print(f"Error sending message: {e}")
            return render_template('compose.html', recipient=recipient, subject=subject_form, body=body)

    # GET request
    return render_template('compose.html', recipient=recipient, subject=subject)

@app.route('/inbox')
@login_required
def inbox():
    user_id = session['user_id']
    # Fetch messages where the current user is the receiver
    # Eager load sender info to avoid N+1 queries in the template
    received_messages = Message.query\
        .options(db.joinedload(Message.sender)) \
        .filter_by(receiver_id=user_id) \
        .order_by(Message.timestamp.desc()) \
        .all()
    return render_template('inbox.html', messages=received_messages)

@app.route('/sent')
@login_required
def sent_messages():
    user_id = session['user_id']
    # Fetch messages where the current user is the sender
    # Eager load receiver info
    sent_messages = Message.query \
        .options(db.joinedload(Message.receiver)) \
        .filter_by(sender_id=user_id) \
        .order_by(Message.timestamp.desc()) \
        .all()
    return render_template('sent.html', messages=sent_messages)

@app.route('/message/<int:message_id>')
@login_required
def view_message(message_id):
    user_id = session['user_id']
    message = Message.query.options(\
        db.joinedload(Message.sender), \
        db.joinedload(Message.receiver) \
    ).get_or_404(message_id)

    # Check if the user is either the sender or receiver
    if user_id != message.sender_id and user_id != message.receiver_id:
        flash('You do not have permission to view this message.', 'error')
        return redirect(url_for('inbox'))

    # Mark as read if the current user is the receiver and it's unread
    was_read = message.is_read # Store original status
    if user_id == message.receiver_id and not message.is_read:
        message.is_read = True
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Error marking message as read: {e}") # Log error but proceed
            message.is_read = was_read # Revert optimistic update on error

    return render_template('message_detail.html', message=message)

# --- Report Card Routes ---

# Route for Teachers to view/edit student report comments
@app.route('/teacher/student-reports', methods=['GET', 'POST'])
@login_required
def manage_student_reports():
    if session['user_role'] != 'teacher':
        flash('Access denied.', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        student_id = request.form.get('student_id')
        comment = request.form.get('report_comment', '').strip()
        student = User.query.get(student_id)

        # Basic validation/authorization (e.g., check if teacher is allowed to comment on this student)
        if not student or student.role != 'student':
             flash('Invalid student selected.', 'error')
        else:
            # Add more specific auth checks if needed
            try:
                student.report_comment = comment
                db.session.commit()
                flash(f"Comment for {student.name} updated.", 'success')
            except Exception as e:
                db.session.rollback()
                flash(f"Error updating comment: {e}", 'error')
                print(f"Error updating comment: {e}")

        return redirect(url_for('manage_student_reports')) # Redirect back to the management page

    # GET Request: List students (potentially only those linked to the teacher)
    # Simplified: Show all students for now. Needs refinement based on teacher-student links.
    students = User.query.filter_by(role='student').order_by(User.name).all()
    return render_template('manage_student_reports.html', students=students)

# Route for Parents to view their child's report card
@app.route('/parent/report-card/<int:child_id>')
@login_required
def view_report_card(child_id):
    if session['user_role'] != 'parent':
        flash('Access denied.', 'error')
        return redirect(url_for('dashboard'))

    parent_email = User.query.get(session['user_id']).email
    child = User.query.get_or_404(child_id)

    # Verify this is the parent's child
    if child.role != 'student' or child.parent_email != parent_email:
        flash('You are not authorized to view this report card.', 'error')
        return redirect(url_for('parent_dashboard'))

    # Fetch quiz attempts
    attempts = QuizAttempt.query.filter_by(student_id=child_id)\
        .join(Quiz)\
        .order_by(QuizAttempt.submitted_at.asc())\
        .all()

    # Calculate cumulative average
    total_attempts = len(attempts)
    cumulative_score = sum(attempt.score for attempt in attempts)
    average_score = cumulative_score / total_attempts if total_attempts > 0 else 0

    # Add grades to attempts
    for attempt in attempts:
        attempt.grade = calculate_grade(attempt.score, attempt.quiz) # Use quiz-specific thresholds

    # Optional: PDF Export Logic
    if request.args.get('format') == 'pdf':
        # This requires a PDF generation library like WeasyPrint or pdfkit
        # html = render_template('report_card_pdf.html', child=child, attempts=attempts, average_score=average_score)
        # pdf = generate_pdf_from_html(html) # Placeholder for your PDF generation function
        # response = make_response(pdf)
        # response.headers['Content-Type'] = 'application/pdf'
        # response.headers['Content-Disposition'] = f'inline; filename=report_card_{child.name}.pdf'
        # return response
        flash('PDF export not yet implemented.', 'info')
        # Fall through to render HTML if PDF fails or isn't implemented

    return render_template('report_card.html',
                           child=child,
                           attempts=attempts,
                           average_score=average_score)

# Decorator for routes requiring admin privileges
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'error')
            return redirect(url_for('login'))
        if session.get('user_role') != 'admin':
            flash('You do not have permission to access this page.', 'error')
            # Redirect non-admins away, perhaps to their own dashboard or home
            user_role = session.get('user_role', 'guest') # Default to 'guest' if role somehow missing
            if user_role == 'teacher':
                return redirect(url_for('teacher_dashboard'))
            elif user_role == 'student':
                return redirect(url_for('student_dashboard'))
            elif user_role == 'parent':
                return redirect(url_for('parent_dashboard'))
            else:
                return redirect(url_for('home')) # Fallback redirect
        return f(*args, **kwargs)
    return decorated_function

@app.route('/admin/dashboard')
@admin_required # Use the new decorator
def admin_dashboard():
    # You can add logic here later to fetch stats for the dashboard
    return render_template('admin/admin_dashboard.html')

@app.route('/admin/users')
@admin_required
def admin_manage_users():
    users = User.query.order_by(User.role, User.name).all()
    return render_template('admin/users.html', users=users)

@app.route('/admin/pending-users')
@admin_required
def admin_pending_users():
    pending_users = User.query.filter_by(is_verified=False).order_by(User.role, User.name).all()
    return render_template('admin/pending_users.html', users=pending_users)

@app.route('/verify-user/<int:user_id>/<token>', methods=['GET'])
def verify_user_by_token(user_id, token):
    """Route for verifying users via email link - doesn't require admin login"""
    user = User.query.get_or_404(user_id)

    # Verify the token matches
    if user.verification_token != token:
        flash('Invalid verification token.', 'error')
        if 'user_id' in session and session.get('user_role') == 'admin':
            return redirect(url_for('admin_pending_users'))
        else:
            return redirect(url_for('login'))

    # Verify the user
    user.is_verified = True
    user.verification_token = None  # Clear the token after verification

    try:
        db.session.commit()

        # If this is a teacher account, ensure they have the preloaded quizzes
        if user.role == 'teacher' and not Quiz.query.filter_by(teacher_id=user.id).first():
            create_preloaded_hard_quiz(user.id)
            print(f"Added preloaded quiz to teacher {user.name} on verification")

        # Send confirmation email to the user
        msg = MailMessage(
            subject='Your Account Has Been Verified',
            recipients=[user.email]
        )
        msg.body = f'''
Dear {user.name},

Your account has been verified by an administrator. You can now log in to the Quiz Management System.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user.name} has been verified successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error verifying user: {e}")
        flash(f'Error verifying user: {e}', 'error')

    # If admin is logged in, redirect to admin panel, otherwise to login page
    if 'user_id' in session and session.get('user_role') == 'admin':
        return redirect(url_for('admin_pending_users'))
    else:
        return redirect(url_for('login'))

@app.route('/admin/verify-user/<int:user_id>/<token>', methods=['GET'])
@admin_required
def admin_verify_user(user_id, token):
    """Route for verifying users from the admin panel - requires admin login"""
    user = User.query.get_or_404(user_id)

    # Verify the token matches
    if user.verification_token != token:
        flash('Invalid verification token.', 'error')
        return redirect(url_for('admin_pending_users'))

    # Verify the user
    user.is_verified = True
    user.verification_token = None  # Clear the token after verification

    try:
        db.session.commit()

        # If this is a teacher account, ensure they have the preloaded quizzes
        if user.role == 'teacher' and not Quiz.query.filter_by(teacher_id=user.id).first():
            create_preloaded_hard_quiz(user.id)
            print(f"Added preloaded quiz to teacher {user.name} on admin verification")

        # Send confirmation email to the user
        msg = MailMessage(
            subject='Your Account Has Been Verified',
            recipients=[user.email]
        )
        msg.body = f'''
Dear {user.name},

Your account has been verified by an administrator. You can now log in to the Quiz Management System.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user.name} has been verified successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error verifying user: {e}")
        flash(f'Error verifying user: {e}', 'error')

    return redirect(url_for('admin_pending_users'))

@app.route('/admin/reject-user/<int:user_id>', methods=['POST'])
@admin_required
def admin_reject_user(user_id):
    user = User.query.get_or_404(user_id)
    user_email = user.email
    user_name = user.name

    try:
        # Delete the user
        db.session.delete(user)
        db.session.commit()

        # Send rejection email
        msg = MailMessage(
            subject='Account Registration Rejected',
            recipients=[user_email]
        )
        msg.body = f'''
Dear {user_name},

We regret to inform you that your account registration for the Quiz Management System has been rejected by an administrator.

If you believe this is an error, please contact the administrator directly.

Thank you,
Quiz Management System Team
'''
        mail.send(msg)

        flash(f'User {user_name} has been rejected and removed from the system.', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error rejecting user: {e}")
        flash(f'Error rejecting user: {e}', 'error')

    return redirect(url_for('admin_pending_users'))

@app.route('/admin/users/edit/<int:user_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_user(user_id):
    user_to_edit = User.query.get_or_404(user_id)

    # Prevent admin from editing their own role or deleting themselves easily
    if user_to_edit.id == session['user_id'] and request.form.get('role') != 'admin':
         flash('Admins cannot change their own role.', 'error')
         return redirect(url_for('admin_edit_user', user_id=user_id))

    if request.method == 'POST':
        try:
            new_name = request.form['name'].strip()
            new_email = request.form['email'].strip().lower()
            new_role = request.form['role']
            new_parent_email = request.form.get('parent_email', '').strip().lower() or None

            # Basic Validation
            if not all([new_name, new_email, new_role]):
                 flash('Name, Email, and Role are required.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            if not is_valid_email(new_email):
                 flash('Invalid email format.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            # Check if email is being changed to one that already exists (excluding the current user)
            existing_user = User.query.filter(User.email == new_email, User.id != user_id).first()
            if existing_user:
                 flash('Email already exists for another user.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

             # Validate role-specific email formats (similar to signup)
            if new_role == 'student' and not new_email.endswith('@jpischool.com'):
                flash('Student email must end with @jpischool.com.', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)
            elif new_role == 'teacher' and not new_email.endswith('@jpischool.com'):
                flash('Teacher email must end with @jpischool.com.', 'error')
                return render_template('admin/edit_user.html', user=user_to_edit)
            elif new_role == 'parent' and not (new_email.endswith('@gmail.com') or new_email.endswith('@yahoo.com')):
                 flash(f'Parent email must end with @gmail.com or @yahoo.com.', 'error')
                 return render_template('admin/edit_user.html', user=user_to_edit)

            # Validate parent email if role is student
            if new_role == 'student':
                if not new_parent_email:
                    flash('Parent email is required for students.', 'error')
                    return render_template('admin/edit_user.html', user=user_to_edit)
                if not is_valid_email(new_parent_email) or not (new_parent_email.endswith('@gmail.com') or new_parent_email.endswith('@yahoo.com')):
                     flash('Invalid format or domain for parent email (must be @gmail.com or @yahoo.com).', 'error')
                     return render_template('admin/edit_user.html', user=user_to_edit)
                # Check if parent exists
                parent_user = User.query.filter_by(email=new_parent_email, role='parent').first()
                if not parent_user:
                     flash('The specified parent email does not belong to a registered parent.', 'error')
                     return render_template('admin/edit_user.html', user=user_to_edit)
            else:
                 new_parent_email = None # Clear parent email if role is not student

            # Update user
            user_to_edit.name = new_name
            user_to_edit.email = new_email
            user_to_edit.role = new_role
            user_to_edit.parent_email = new_parent_email

            db.session.commit()
            flash(f'User {user_to_edit.name} updated successfully.', 'success')
            return redirect(url_for('admin_manage_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error updating user: {e}', 'error')
            print(f"Error editing user {user_id}: {e}\n{traceback.format_exc()}") # Log detailed error

    # GET request: Render the edit form
    return render_template('admin/edit_user.html', user=user_to_edit)

@app.route('/admin/users/edit-password/<int:user_id>', methods=['POST'])
@admin_required
def admin_edit_user_password(user_id):
    user_to_edit = User.query.get_or_404(user_id)
    new_password = request.form.get('new_password', '').strip()
    confirm_password = request.form.get('confirm_password', '').strip()

    # Validate new password
    if not new_password:
        flash('New password is required.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    if new_password != confirm_password:
        flash('New passwords do not match.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    if not is_strong_password(new_password):
        flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
        return redirect(url_for('admin_edit_user', user_id=user_id))

    try:
        user_to_edit.password = generate_password_hash(new_password)
        user_to_edit.unhashed_password = new_password  # Store unhashed version
        db.session.commit()
        flash(f'Password for {user_to_edit.name} updated successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error updating password: {e}', 'error')
        print(f"Error updating password for user {user_id}: {e}\n{traceback.format_exc()}")

    return redirect(url_for('admin_edit_user', user_id=user_id))

@app.route('/admin/users/delete/<int:user_id>', methods=['POST'])
@admin_required
def admin_delete_user(user_id):
    user_to_delete = User.query.get_or_404(user_id)

    # Prevent admin from deleting themselves
    if user_to_delete.id == session['user_id']:
        flash('You cannot delete your own admin account.', 'error')
        return redirect(url_for('admin_manage_users'))

    # Prevent deletion of the last remaining admin account
    admin_count = User.query.filter_by(role='admin').count()
    if user_to_delete.role == 'admin' and admin_count <= 1:
        flash('Cannot delete the last admin account.', 'error')
        return redirect(url_for('admin_manage_users'))

    try:
        # --- Handle related data before deleting user ---
        # Example: Reassign quizzes if teacher, nullify attempts if student, delete messages?
        # For now, we will just delete the user. Consider FK constraints or soft delete later.

        # Delete related messages (sent or received)
        Message.query.filter(or_(Message.sender_id == user_id, Message.receiver_id == user_id)).delete(synchronize_session=False)

        # Delete related quiz attempts (if student)
        if user_to_delete.role == 'student':
             QuizAttempt.query.filter_by(student_id=user_id).delete(synchronize_session=False)
             # Also delete associated answers implicitly due to attempt deletion or handle explicitly if needed
             # QuizAnswer.query.join(QuizAttempt).filter(QuizAttempt.student_id == user_id).delete(synchronize_session=False)

        # What to do with Quizzes if a Teacher is deleted?
        # Option 1: Delete them (cascading effect)
        # Option 2: Set teacher_id to NULL (if nullable)
        # Option 3: Reassign to a default admin/teacher (complex)
        # For now, let's delete the quizzes (assuming cascade delete isn't set up in model)
        if user_to_delete.role == 'teacher':
             quizzes_to_delete = Quiz.query.filter_by(teacher_id=user_id).all()
             for quiz in quizzes_to_delete:
                 Question.query.filter_by(quiz_id=quiz.id).delete(synchronize_session=False)
                 # Add deletion for QuizAttempts/Answers related to this quiz if necessary
                 QuizAttempt.query.filter_by(quiz_id=quiz.id).delete(synchronize_session=False)
                 # QuizAnswer ...
                 db.session.delete(quiz)

        # Now delete the user
        user_name = user_to_delete.name # Get name before deleting
        db.session.delete(user_to_delete)
        db.session.commit()
        flash(f'User {user_name} and their associated data (messages, attempts, quizzes) deleted successfully.', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting user: {e}', 'error')
        print(f"Error deleting user {user_id}: {e}\n{traceback.format_exc()}")

    return redirect(url_for('admin_manage_users'))

@app.route('/admin/quizzes')
@admin_required
def admin_manage_quizzes():
    # Eager load the teacher relationship to avoid N+1 queries
    quizzes = Quiz.query.options(db.joinedload(Quiz.teacher)).order_by(Quiz.created_at.desc()).all()
    return render_template('admin/quizzes.html', quizzes=quizzes)

@app.route('/admin/statistics')
@admin_required
def admin_statistics():
    # Get user counts by role
    user_counts = {
        'admin': User.query.filter_by(role='admin').count(),
        'teacher': User.query.filter_by(role='teacher').count(),
        'student': User.query.filter_by(role='student').count(),
        'parent': User.query.filter_by(role='parent').count()
    }

    # Get quiz statistics
    total_quizzes = Quiz.query.count()
    total_questions = Question.query.count()
    total_attempts = QuizAttempt.query.count()

    # Calculate average scores
    avg_score = db.session.query(db.func.avg(QuizAttempt.score)).scalar() or 0

    # Get recent activity with eager loading
    recent_attempts = QuizAttempt.query\
        .options(
            db.joinedload(QuizAttempt.student),
            db.joinedload(QuizAttempt.quiz)
        )\
        .order_by(QuizAttempt.submitted_at.desc())\
        .limit(10)\
        .all()

    recent_messages = Message.query\
        .options(
            db.joinedload(Message.sender),
            db.joinedload(Message.receiver)
        )\
        .order_by(Message.timestamp.desc())\
        .limit(10)\
        .all()

    return render_template('admin/statistics.html',
                         user_counts=user_counts,
                         total_quizzes=total_quizzes,
                         total_questions=total_questions,
                         total_attempts=total_attempts,
                         avg_score=avg_score,
                         recent_attempts=recent_attempts,
                         recent_messages=recent_messages)

@app.route('/admin/settings', methods=['GET', 'POST'])
@admin_required
def admin_settings():
    if request.method == 'POST':
        # Handle settings updates
        try:
            # Example: Update session lifetime
            new_lifetime = int(request.form.get('session_lifetime', 7))
            app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=new_lifetime)

            # Add more settings as needed

            flash('Settings updated successfully!', 'success')
        except Exception as e:
            flash(f'Error updating settings: {e}', 'error')

    # Get current settings
    settings = {
        'session_lifetime': app.config['PERMANENT_SESSION_LIFETIME'].days,
        'max_quiz_questions': 20,  # Default value
        'min_quiz_questions': 5,   # Default value
        'max_quiz_time': 60,       # Default value in minutes
        'smtp_server': app.config.get('MAIL_SERVER', ''),
        'smtp_port': app.config.get('MAIL_PORT', ''),
        'smtp_username': app.config.get('MAIL_USERNAME', ''),
        'smtp_password': app.config.get('MAIL_PASSWORD', '')
    }

    return render_template('admin/settings.html', settings=settings)

def generate_reset_token(email):
    return serializer.dumps(email, salt=app.config['SECURITY_PASSWORD_SALT'])

def verify_reset_token(token, expiration=3600):
    try:
        email = serializer.loads(
            token,
            salt=app.config['SECURITY_PASSWORD_SALT'],
            max_age=expiration
        )
        return email
    except:
        return None

@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form.get('email', '').strip().lower()
        user = User.query.filter_by(email=email).first()

        if user:
            # Generate token
            token = generate_reset_token(email)
            reset_url = url_for('reset_password', token=token, _external=True)

            # Send email
            try:
                msg = MailMessage(
                    subject='Password Reset Request',
                    recipients=[email],
                    body=f'''To reset your password, visit the following link:
{reset_url}

If you did not make this request then simply ignore this email.
''',
                    html=f'''<p>To reset your password, visit the following link:</p>
<p><a href="{reset_url}">{reset_url}</a></p>
<p>If you did not make this request then simply ignore this email.</p>
'''
                )
                mail.send(msg)
                flash('Password reset instructions have been sent to your email.', 'success')
            except Exception as e:
                print(f"Error sending reset email: {e}")
                flash('Error sending reset email. Please try again.', 'error')
        else:
            # Don't reveal if email exists or not
            flash('If your email is registered, you will receive password reset instructions.', 'info')

        return redirect(url_for('login'))

    return render_template('forgot_password.html')

@app.route('/reset-password/<token>', methods=['GET', 'POST'])
def reset_password(token):
    email = verify_reset_token(token)
    if not email:
        flash('The password reset link is invalid or has expired.', 'error')
        return redirect(url_for('forgot_password'))

    if request.method == 'POST':
        user = User.query.filter_by(email=email).first()
        if not user:
            flash('User not found.', 'error')
            return redirect(url_for('forgot_password'))

        new_password = request.form.get('new_password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()

        if not new_password:
            flash('New password is required.', 'error')
            return render_template('reset_password.html', token=token)

        if new_password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('reset_password.html', token=token)

        if not is_strong_password(new_password):
            flash('Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.', 'error')
            return render_template('reset_password.html', token=token)

        try:
            user.password = generate_password_hash(new_password)
            user.unhashed_password = new_password  # Update unhashed version too
            db.session.commit()
            flash('Your password has been updated successfully. Please login with your new password.', 'success')
            return redirect(url_for('login'))
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating password: {e}', 'error')
            return render_template('reset_password.html', token=token)

    return render_template('reset_password.html', token=token)

if __name__ == '__main__':
    app.run(debug=True)
