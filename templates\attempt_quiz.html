{% extends "base.html" %}

{% block title %}{{ quiz.title }} - Attempt Quiz{% endblock %}

{% block content %}
<div class="container quiz-attempt-container">
    <div class="quiz-header">
        <h1>{{ quiz.title }}</h1>
        <div id="timer">Time Left: <span>{{ quiz.time_limit }}:00</span></div>
    </div>
    {% if quiz.description %}
        <p class="quiz-description">{{ quiz.description }}</p>
    {% endif %}

    <form id="quiz-form" method="post" action="{{ url_for('submit_quiz', quiz_id=quiz.id) }}">
        {% for question in questions %}
        <div class="question-card" id="question-{{ question.id }}">
            <div class="question-header">
                 <span class="question-number">Question {{ loop.index }}</span>
                 <span class="question-marks">({{ question.marks }} Marks)</span>
            </div>
           
            <p class="question-text">{{ question.question_text }}</p>

            <div class="options">
                {% if question.question_type == 'mcq' %}
                    {% set options = [question.option1, question.option2, question.option3, question.option4] %}
                    {% for i in range(options|length) %}
                        {% if options[i] %}
                            <div class="option-item">
                                <input type="radio" id="q{{ question.id }}_opt{{ i+1 }}" name="question_{{ question.id }}" value="{{ i+1 }}">
                                <label for="q{{ question.id }}_opt{{ i+1 }}" class="option-label">
                                    <span class="option-text">{{ options[i] }}</span>
                                    <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                                </label>
                            </div>
                        {% endif %}
                    {% endfor %}
                {% elif question.question_type == 'true_false' %}
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_true" name="question_{{ question.id }}" value="True">
                         <label for="q{{ question.id }}_true" class="option-label">
                            <span class="option-text">True</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                     <div class="option-item">
                        <input type="radio" id="q{{ question.id }}_false" name="question_{{ question.id }}" value="False">
                         <label for="q{{ question.id }}_false" class="option-label">
                            <span class="option-text">False</span>
                             <button type="button" class="strike-btn" onclick="toggleStrike(this)"><s>S</s></button>
                        </label>
                    </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <button type="submit" class="btn submit-btn">Submit Quiz</button>
    </form>
</div>

<script>
// --- Timer Logic --- 
const timerDisplay = document.querySelector('#timer span');
const quizForm = document.getElementById('quiz-form');
// Ensure time_limit is treated as a number, default to 0 if invalid
const timeLimitMinutes = parseInt("{{ quiz.time_limit | default(0, true) }}", 10) || 0;
let timeLeft = timeLimitMinutes * 60; // Time in seconds

function updateTimer() {
    const minutes = Math.floor(timeLeft / 60);
    let seconds = timeLeft % 60;
    seconds = seconds < 10 ? '0' + seconds : seconds;
    timerDisplay.textContent = `${minutes}:${seconds}`;
    
    if (timeLeft <= 0) {
        clearInterval(timerInterval);
        alert('Time is up! Submitting your quiz.');
        quizForm.submit();
    } else {
        timeLeft--;
    }
}

const timerInterval = setInterval(updateTimer, 1000);

// --- Confirmation Before Submit --- 
quizForm.addEventListener('submit', function(event) {
    if (timeLeft > 0) { // Only ask for confirmation if time is not up
        const confirmation = confirm('Are you sure you want to submit your quiz?');
        if (!confirmation) {
            event.preventDefault(); // Stop submission if user cancels
        }
    }
    // If time is up, form submits automatically without confirmation
});

// --- Strikethrough Logic --- 
function toggleStrike(button) {
    const label = button.closest('.option-label');
    const textSpan = label.querySelector('.option-text');
    textSpan.classList.toggle('strikethrough');
}

</script>

<style>
.quiz-attempt-container {
    max-width: 900px;
    margin: 2rem auto;
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 8px;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

#timer {
    font-size: 1.2em;
    font-weight: bold;
    color: #dc3545;
}

.quiz-description {
    background-color: #e9ecef;
    padding: 1rem;
    border-radius: 5px;
    margin-bottom: 2rem;
    color: #495057;
}

.question-card {
    background-color: #fff;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #007bff;
}

.question-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
    line-height: 1.6;
}

.options {
    display: flex;
    flex-direction: column;
    gap: 0.8rem; /* Spacing between options */
}

.option-item {
    display: flex; /* Align items for spacing */
    align-items: center; 
}

/* Hide the default radio button */
.option-item input[type="radio"] {
   position: absolute;
   opacity: 0;
   width: 0;
   height: 0;
}

.option-label {
    display: flex; /* Use flex to align text and button */
    align-items: center;
    width: 100%; /* Make label take full width */
    padding: 0.8rem 1rem;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: #fff;
    cursor: pointer;
    transition: background-color 0.2s, border-color 0.2s;
}

/* Style the label when the radio is checked */
.option-item input[type="radio"]:checked + .option-label {
    background-color: #e2f0ff; /* Light blue background */
    border-color: #007bff;
}

.option-label:hover {
    background-color: #f1f3f5;
}

.option-text {
    flex-grow: 1; /* Allow text to take available space */
    margin-right: 10px; /* Space between text and strike button */
    transition: text-decoration 0.2s;
}

.strike-btn {
    background: none;
    border: 1px solid #adb5bd;
    color: #6c757d;
    padding: 2px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8em;
    line-height: 1;
    margin-left: auto; /* Push button to the right */
}

.strike-btn s {
    text-decoration: none; /* Remove default strikethrough */
}

.strike-btn:hover {
    background-color: #e9ecef;
}

.option-text.strikethrough {
    text-decoration: line-through;
    color: #adb5bd; /* Grey out struck-through text */
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    font-size: 1.1em;
    margin-top: 2rem;
}

</style>
{% endblock %} 