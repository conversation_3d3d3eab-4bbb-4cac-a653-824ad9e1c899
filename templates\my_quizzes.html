<!DOCTYPE html>
<html>
<head>
    <title>My Quizzes - Quiz Management System</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <header>
        <div class="container">
            <h1>My Quizzes</h1>
            <nav>
                <a href="/teacher/dashboard" class="btn">Back to Dashboard</a>
                <a href="/logout" class="btn btn-danger">Logout</a>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <section class="dashboard-section">
            <div class="section-header">
                <h2>My Created Quizzes</h2>
                <a href="/teacher/create-quiz" class="btn">Create New Quiz</a>
            </div>
            
            <div class="quizzes-grid">
                {% for quiz in quizzes %}
                <div class="quiz-card">
                    <div class="quiz-header">
                        <h3>{{ quiz.title }}</h3>
                        <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty|title }}</span>
                    </div>
                    
                    <div class="quiz-details">
                        <p class="quiz-description">{{ quiz.description or 'No description provided' }}</p>
                        <div class="quiz-meta">
                            <span>Time: {{ quiz.time_limit }} mins</span>
                            <span>Total Marks: {{ quiz.total_marks }}</span>
                            <span>Questions: {{ quiz.questions|length }}</span>
                        </div>
                        <div class="grade-thresholds">
                            <span>A: ≥{{ quiz.grade_a_threshold }}%</span>
                            <span>B: ≥{{ quiz.grade_b_threshold }}%</span>
                            <span>C: ≥{{ quiz.grade_c_threshold }}%</span>
                            <span>D: ≥{{ quiz.grade_d_threshold }}%</span>
                        </div>
                    </div>
                    
                    <div class="quiz-actions">
                        <a href="/teacher/edit-quiz/{{ quiz.id }}" class="btn btn-secondary">Edit</a>
                        <a href="/teacher/view-quiz/{{ quiz.id }}" class="btn">View</a>
                        <button onclick="deleteQuiz({{ quiz.id }})" class="btn btn-danger">Delete</button>
                    </div>
                </div>
                {% else %}
                <div class="no-quizzes">
                    <p>You haven't created any quizzes yet.</p>
                    <a href="/teacher/create-quiz" class="btn">Create Your First Quiz</a>
                </div>
                {% endfor %}
            </div>
        </section>
    </main>

    <script>
        function deleteQuiz(quizId) {
            if (confirm('Are you sure you want to delete this quiz? This action cannot be undone.')) {
                fetch(`/teacher/delete-quiz/${quizId}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Failed to delete quiz. Please try again.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while deleting the quiz.');
                });
            }
        }
    </script>
</body>
</html> 