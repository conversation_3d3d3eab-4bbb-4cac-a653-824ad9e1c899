/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header styles */
header {
    background-color: #2c3e50;
    color: white;
    padding: 2rem 0;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

header nav {
    margin-top: 1rem;
}

/* Main content styles */
main {
    padding: 3rem 0;
}

/* Dashboard styles */
.dashboard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.card {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

.card h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.card p {
    color: #666;
    margin-bottom: 1.5rem;
}

/* Form styles */
.form-container {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    margin: 0 auto;
}

.form-container h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
    text-align: left;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

/* Quiz creation specific styles */
#questions-container {
    margin-top: 2rem;
}

.question-group {
    background-color: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #eee;
}

.options-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 0.5rem;
}

.options-container input {
    width: 100%;
}

.email-hint {
    display: block;
    margin-top: 0.5rem;
    color: #666;
    font-size: 0.875rem;
    line-height: 1.4;
}

/* Button styles */
.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: #3498db;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    margin: 0.5rem;
    transition: background-color 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.btn:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: #2ecc71;
}

.btn-secondary:hover {
    background-color: #27ae60;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Navigation styles */
nav {
    margin-top: 1.5rem;
}

.dashboard-section {
    margin: 2rem 0;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    margin: 0;
    color: #2c3e50;
}

.quizzes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.quiz-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.quiz-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #333;
}

.difficulty-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.difficulty-badge.easy {
    background-color: #e6f7e6;
    color: #2e7d32;
}

.difficulty-badge.medium {
    background-color: #fff3e0;
    color: #f57c00;
}

.difficulty-badge.hard {
    background-color: #ffebee;
    color: #c62828;
}

.quiz-details {
    flex-grow: 1;
}

.quiz-description {
    color: #666;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.quiz-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
}

.grade-thresholds {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 0.5rem 0;
    font-size: 0.9rem;
}

.grade-thresholds span {
    background-color: #f5f5f5;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.quiz-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.no-quizzes {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    grid-column: 1 / -1;
}

.no-quizzes p {
    color: #666;
    margin-bottom: 1rem;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
} 