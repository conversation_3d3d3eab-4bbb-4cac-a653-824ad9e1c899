from werkzeug.security import generate_password_hash
from app import app, db, User

def initialize_database():
    """Initialize the database with tables and admin user"""

    try:
        with app.app_context():
            # Create tables if they don't exist
            db.create_all()
            print("Database tables created successfully.")

            # Check if admin user already exists
            admin = User.query.filter_by(email='<EMAIL>').first()

            if not admin:
                # Create admin user
                admin = User(
                    name='Admin',
                    email='<EMAIL>',
                    password=generate_password_hash('admin123'),
                    unhashed_password='admin123',
                    role='admin',
                    is_verified=True
                )

                db.session.add(admin)
                db.session.commit()
                print("Admin user created successfully.")
            else:
                print("Admin user already exists.")

            print("Database initialization completed successfully.")
            return True
    except Exception as e:
        print(f"Error initializing database: {e}")
        return False

if __name__ == "__main__":
    print("Initializing database...")
    if initialize_database():
        print("Database initialization completed successfully.")
    else:
        print("Database initialization failed.")
