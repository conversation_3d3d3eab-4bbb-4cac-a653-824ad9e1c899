{% extends "base.html" %}

{% block content %}
    {# Remove the duplicate header section #}
    {# <header>
        <div class="container">
            <h1>Quiz Platform</h1>
            <!-- Add navigation if needed later -->
        </div>
    </header> #}

    <main class="page-content">
        <div class="login-form">
            <h1>Login</h1>
            <form method="POST" id="loginForm">
                <!-- Add flash message placeholders if not already present -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                  {% if messages %}
                    {% for category, message in messages %}
                      <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                  {% endif %}
                {% endwith %}
                <div id="error-message-div" class="error-message" style="display: none;"></div>
                <div id="success-message-div" class="success-message" style="display: none;"></div>
                
                <div class="form-group">
                    <label for="email">Email</label>
                    <div class="input-wrapper">
                        <input required type="email" id="email" name="email" placeholder="Enter your email">
                    </div> 
                </div>
                
                <div class="form-group">
                     <label for="password">Password</label>
                     <div class="input-wrapper">
                         <input required type="password" id="password" name="password" placeholder="Enter your password">
                     </div>
                </div>
                
                <div class="text-center mt-3">
                    <button type="submit" class="btn btn-primary">Login</button>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('forgot_password') }}">Forgot Password?</a>
                </div>
            </form>
            
            <p class="signup-link">Don't have an account? <a href="{{ url_for('signup') }}">Sign up</a></p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2024 Quiz Management System. All rights reserved.</p>
        </div>
    </footer>
{% endblock %}

{% block styles %}
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
<style>
    :root {
        /* Color System */
        --primary-color: #4A90E2;
        --primary-dark: #357ABD;
        --primary-light: #6BA4E7;
        --secondary-color: #2C3E50;
        --accent-color: #E74C3C;
        --text-primary: #2C3E50;
        --text-secondary: #7F8C8D;
        --background-light: #F5F7FA;
        --background-dark: #E4E9F2;
        --white: #FFFFFF;
        --error: #E74C3C;
        --success: #2ECC71;
        --warning: #F1C40F;
        
        /* Spacing System */
        --spacing-xs: 0.25rem;
        --spacing-sm: 0.5rem;
        --spacing-md: 1rem;
        --spacing-lg: 1.5rem;
        --spacing-xl: 2rem;
        --spacing-xxl: 3rem;
        
        /* Border Radius */
        --radius-sm: 4px;
        --radius-md: 8px;
        --radius-lg: 15px;
        --radius-full: 100px;
        
        /* Shadows */
        --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
        --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
        --shadow-lg: 0 5px 15px rgba(0,0,0,0.1);
    }

    body {
        font-family: 'Poppins', sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, var(--background-light) 0%, var(--background-dark) 100%);
        color: var(--text-primary);
        line-height: 1.6;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .container {
        width: 90%;
        max-width: 1100px;
        margin: 0 auto;
        padding: 0 var(--spacing-md);
    }

    main.page-content {
        flex-grow: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-xl) 0;
    }

    .login-form {
        background-color: var(--white);
        padding: var(--spacing-xxl);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-lg);
        width: 100%;
        max-width: 400px;
        box-sizing: border-box;
    }

    .login-form h1 {
        text-align: center;
        margin-bottom: var(--spacing-xl);
        color: var(--secondary-color);
        font-weight: 700;
    }

    .form-group {
        margin-bottom: var(--spacing-lg);
    }

    .form-group label {
        display: block;
        margin-bottom: var(--spacing-sm);
        color: var(--text-secondary);
        font-weight: 600;
    }

    .input-wrapper {
      position: relative;
        width: 100%;
    }

    .input-wrapper input {
      width: 100%;
        padding: var(--spacing-md);
        border: 2px solid var(--background-dark);
        border-radius: var(--radius-md);
        font-size: 1rem;
        color: var(--text-primary);
        background-color: var(--white);
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .input-wrapper input:focus {
      outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px var(--primary-light);
    }

    .input-wrapper input::placeholder {
        color: var(--text-secondary);
        opacity: 0.7;
    }

    .auth-button {
        width: 100%;
        padding: var(--spacing-md) var(--spacing-xl);
        background-color: var(--primary-color);
        color: var(--white);
        border: none;
        border-radius: var(--radius-full);
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
      transition: all 0.3s ease;
    }

    .auth-button:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
    }

    .auth-button:active {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }

    .signup-link {
        text-align: center;
        margin-top: var(--spacing-lg);
        color: var(--text-secondary);
    }

    .signup-link a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 600;
    }

    .signup-link a:hover {
        text-decoration: underline;
    }

    .flash-message {
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-md);
        border-radius: var(--radius-md);
        color: var(--white);
    }

    .flash-message.success {
        background-color: var(--success);
    }

    .flash-message.error {
        background-color: var(--error);
    }

    .error-message {
        color: var(--error);
        font-size: 0.9rem;
        margin-top: var(--spacing-xs);
    }

    footer {
        text-align: center;
        padding: var(--spacing-md) 0;
        margin-top: auto;
        color: var(--text-secondary);
        font-size: 0.9em;
    }

    @media (max-width: 480px) {
        .login-form {
            padding: var(--spacing-xl);
            margin: var(--spacing-md);
        }
    }
</style>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const errorDiv = document.getElementById('error-message-div'); 
    const successDiv = document.getElementById('success-message-div');
    
    // Check for flash messages rendered by Flask
    const flashMessages = document.querySelectorAll('.flash-message');
    let hasFlashError = false;
    let hasFlashSuccess = false;
    flashMessages.forEach(message => {
        const category = message.classList.contains('success') ? 'success' : 'error'; // Simplified category check
        if (category === 'error') {
            errorDiv.textContent = message.textContent;
            errorDiv.style.display = 'block';
            hasFlashError = true;
        } else { // Success
            successDiv.textContent = message.textContent;
            successDiv.style.display = 'block';
            hasFlashSuccess = true;
        }
        message.remove(); // Remove the original flash message element
    });
    
    // Auto-hide messages after 5 seconds only if they were displayed
    if (hasFlashError || hasFlashSuccess) {
        setTimeout(() => {
            if (hasFlashError) errorDiv.style.display = 'none';
            if (hasFlashSuccess) successDiv.style.display = 'none';
        }, 5000);
    }
});
</script>
{% endblock %}