{% extends "base.html" %}

{% block title %}Verify OTP - Quiz Management System{% endblock %}

{% block content %}
<main class="hero">
    <div class="container">
        <div class="hero-content" style="max-width: 400px; margin: 0 auto;">
            <h2 style="margin-bottom: 0.5em; color: var(--secondary-color); font-weight: 700;">Verify Your Email</h2>
            <p class="verification-message">We've sent a 6-digit verification code to your email address.</p>

            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="flash-message {{ category }}">{{ message }}</div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('verify_otp') }}" id="otp-form">
                <div class="form-group" style="margin-bottom: 1.5em;">
                    <label for="otp" style="font-weight: 600; color: var(--text-primary);">Enter Verification Code</label>
                    <div class="input-wrapper" style="margin-top: 0.5em;">
                        <input required type="text" id="otp" name="otp" 
                               pattern="[0-9]{6}" maxlength="6" 
                               placeholder="Enter 6-digit code"
                               title="Please enter the 6-digit code sent to your email"
                               style="width: 100%; padding: 0.75em 1em; border: 1px solid #ccc; border-radius: var(--radius-full); font-size: 1.1em;">
                    </div>
                </div>

                <button type="submit" class="animated-button" style="width: 100%; margin-bottom: 1em;">
                    Verify Code
                </button>

                <div class="otp-actions">
                    <p style="margin: 0;">Didn't receive the code? 
                        <a href="{{ url_for('resend_otp') }}" class="resend-link">Resend Code</a>
                    </p>
                </div>
            </form>
        </div>
    </div>
</main>

<style>
.verification-message {
    text-align: center;
    margin-bottom: 20px;
    color: var(--text-secondary);
}
.otp-actions {
    text-align: center;
    margin-top: 20px;
}
.resend-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
}
.resend-link:hover {
    text-decoration: underline;
}
.flash-message {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    color: white;
    text-align: center;
}
.flash-message.success { background-color: #28a745; }
.flash-message.error { background-color: #dc3545; }
.flash-message.info { background-color: #17a2b8; }

@media (max-width: 480px) {
    .hero-content {
        padding: var(--spacing-xl);
    }
    .animated-button {
        width: 100%;
    }
}
</style>
{% endblock %} 