{% extends "base.html" %}

{% block title %}Edit User - {{ user.name }}{% endblock %}

{% block content %}
<main class="page-content admin-panel">
    <div class="container">
        <h1>Edit User: {{ user.name }}</h1>
        <a href="{{ url_for('admin_manage_users') }}" class="back-link">&larr; Back to User List</a>

        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="flash-message {{ category }}">{{ message }}</div>
            {% endfor %}
          {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('admin_edit_user', user_id=user.id) }}" class="edit-user-form">
            <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" name="name" value="{{ user.name }}" required class="form-control">
            </div>
            <div class="form-group">
                <label for="email">Email</label>
                <input type="email" id="email" name="email" value="{{ user.email }}" required class="form-control">
                <small class="email-hint" id="emailHint"></small> {# Hint updated by JS #}
            </div>
            <div class="form-group">
                <label for="role">Role</label>
                <select id="role" name="role" required class="form-control">
                    <option value="student" {% if user.role == 'student' %}selected{% endif %}>Student</option>
                    <option value="teacher" {% if user.role == 'teacher' %}selected{% endif %}>Teacher</option>
                    <option value="parent" {% if user.role == 'parent' %}selected{% endif %}>Parent</option>
                    <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin</option>
                </select>
            </div>
            <div class="form-group" id="parent-email-group" style="display: {% if user.role == 'student' %}block{% else %}none{% endif %};">
                 <label for="parent_email">Parent's Email (Required for Students)</label>
                 <input type="email" id="parent_email" name="parent_email" value="{{ user.parent_email or '' }}" class="form-control">
                 <small class="email-hint">Must end with @gmail.com or @yahoo.com</small>
            </div>

            <button type="submit" class="btn btn-save">Save Changes</button>
        </form>

        <div class="password-section">
            <h2>Change Password</h2>
            <form method="POST" action="{{ url_for('admin_edit_user_password', user_id=user.id) }}" class="edit-password-form">
                <div class="form-group">
                    <label for="current_password">Current Password</label>
                    <div class="password-input-group">
                        <input type="text" id="current_password" name="current_password" readonly class="form-control" value="{{ user.unhashed_password or '••••••••' }}">
                        <div class="password-buttons">
                            <button type="button" class="btn btn-secondary copy-password" data-target="current_password">
                                <i class="fas fa-copy"></i>
                            </button>
                            <button type="button" class="btn btn-secondary toggle-password" data-target="current_password">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <div class="password-input-group">
                        <input type="password" id="new_password" name="new_password" required class="form-control">
                        <button type="button" class="btn btn-secondary toggle-password" data-target="new_password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <small class="password-hint">Password must be at least 8 characters long and include an uppercase letter, a lowercase letter, a digit, and a special character.</small>
                </div>
                <div class="form-group">
                    <label for="confirm_password">Confirm New Password</label>
                    <div class="password-input-group">
                        <input type="password" id="confirm_password" name="confirm_password" required class="form-control">
                        <button type="button" class="btn btn-secondary toggle-password" data-target="confirm_password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
                <button type="submit" class="btn btn-warning">Update Password</button>
            </form>
        </div>
    </div>
</main>

<style>
/* Inherits styles from admin_dashboard.html and users.html */

.edit-user-form {
    margin-top: 2rem;
    max-width: 600px; /* Limit form width */
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    box-sizing: border-box;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: dodgerblue;
    outline: none;
    box-shadow: 0 0 0 2px rgba(30, 144, 255, 0.2);
}

select.form-control {
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23007bff%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right .7em top 50%;
    background-size: .65em auto;
    padding-right: 2.5em;
    background-color: white;
}

.email-hint {
    display: block;
    font-size: 0.8em;
    margin-top: 5px;
    color: #777;
}

.btn-save {
    background-color: #28a745; /* Green */
    color: white;
    padding: 0.8rem 1.5rem;
    font-size: 1em;
}
.btn-save:hover {
    background-color: #218838;
}

.password-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #eee;
}

.password-section h2 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 1.5rem;
}

.edit-password-form {
    max-width: 600px;
}

.password-hint {
    display: block;
    font-size: 0.8em;
    margin-top: 5px;
    color: #777;
}

.btn-warning {
    background-color: #ffc107;
    color: #000;
    padding: 0.8rem 1.5rem;
    font-size: 1em;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.password-input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-group .form-control {
    padding-right: 40px; /* Make room for the eye icon */
}

.toggle-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    color: #666;
}

.toggle-password:hover {
    color: #333;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.5rem 1rem;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.copy-password {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 5px;
    cursor: pointer;
    color: #666;
}

.copy-password:hover {
    color: #333;
}

.copy-password.copied {
    color: #28a745;
}

.form-control[readonly] {
    background-color: #f8f9fa;
    cursor: default;
}

.password-buttons {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
}

.password-buttons .btn {
    padding: 5px;
    background: none;
    border: none;
    color: #666;
}

.password-buttons .btn:hover {
    color: #333;
}

.password-buttons .btn.copied {
    color: #28a745;
}
</style>

<script>
// Simple JS to show/hide parent email based on role selection
document.addEventListener('DOMContentLoaded', function() {
    const roleSelect = document.getElementById('role');
    const parentEmailGroup = document.getElementById('parent-email-group');
    const parentEmailInput = document.getElementById('parent_email');
    const emailInput = document.getElementById('email');
    const emailHint = document.getElementById('emailHint');

    function toggleParentEmail() {
        const selectedRole = roleSelect.value;
        if (selectedRole === 'student') {
            parentEmailGroup.style.display = 'block';
            parentEmailInput.required = true; // Make required if student
        } else {
            parentEmailGroup.style.display = 'none';
            parentEmailInput.required = false;
            parentEmailInput.value = ''; // Clear if not student
        }
        updateEmailHint(emailInput.value, selectedRole);
    }

    function updateEmailHint(email, role) {
        if (role === 'student') {
             emailHint.textContent = 'Must end with @jpischool.com';
        } else if (role === 'teacher') {
            emailHint.textContent = 'Must end with @jpischool.com';
        } else if (role === 'parent') {
            emailHint.textContent = 'Must end with @gmail.com or @yahoo.com';
        } else {
             emailHint.textContent = ''; // Clear for admin or others
        }
    }

    roleSelect.addEventListener('change', toggleParentEmail);
    emailInput.addEventListener('input', function() {
        updateEmailHint(this.value, roleSelect.value);
    });

    // Initial setup on page load
    toggleParentEmail();

    // Toggle password visibility
    const toggleButtons = document.querySelectorAll('.toggle-password');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const input = document.getElementById(targetId);
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Copy password functionality
    const copyButtons = document.querySelectorAll('.copy-password');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const input = document.getElementById(targetId);

            // Copy to clipboard
            navigator.clipboard.writeText(input.value).then(() => {
                // Visual feedback
                const icon = this.querySelector('i');
                icon.classList.remove('fa-copy');
                icon.classList.add('fa-check');
                this.classList.add('copied');

                // Reset after 2 seconds
                setTimeout(() => {
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-copy');
                    this.classList.remove('copied');
                }, 2000);
            });
        });
    });

    // Update current password field when new password is entered
    const newPassword = document.getElementById('new_password');
    const currentPassword = document.getElementById('current_password');

    newPassword.addEventListener('input', function() {
        if (this.value) {
            currentPassword.value = this.value;
        } else {
            currentPassword.value = '{{ user.unhashed_password or "••••••••" }}';
        }
    });

    // Password confirmation validation
    const confirmPassword = document.getElementById('confirm_password');
    const form = document.querySelector('.edit-password-form');

    form.addEventListener('submit', function(e) {
        if (newPassword.value !== confirmPassword.value) {
            e.preventDefault();
            alert('New passwords do not match!');
        }
    });
});
</script>
{% endblock %}
