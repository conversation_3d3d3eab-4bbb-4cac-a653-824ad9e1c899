{% extends "base.html" %}

{% block title %}Admin - View Quiz{% endblock %}

{% block content %}
<div class="container admin-view-quiz-container">
    <div class="admin-header">
        <h1>{{ quiz.title }}</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin_edit_quiz', quiz_id=quiz.id) }}" class="btn btn-primary">Edit Quiz</a>
            <a href="{{ url_for('admin_manage_quizzes') }}" class="btn btn-secondary">Back to Quiz Management</a>
        </div>
    </div>

    <!-- Quiz Information -->
    <div class="quiz-info-section">
        <div class="info-grid">
            <div class="info-card">
                <h3>Quiz Details</h3>
                <div class="info-item">
                    <span class="label">Title:</span>
                    <span class="value">{{ quiz.title }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Description:</span>
                    <span class="value">{{ quiz.description or 'No description provided' }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Assigned Teacher:</span>
                    <span class="value">{{ quiz.teacher.name }} ({{ quiz.teacher.email }})</span>
                </div>
                <div class="info-item">
                    <span class="label">Difficulty:</span>
                    <span class="difficulty-badge difficulty-{{ quiz.difficulty|lower }}">{{ quiz.difficulty|capitalize }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Time Limit:</span>
                    <span class="value">{{ quiz.time_limit }} minutes</span>
                </div>
                <div class="info-item">
                    <span class="label">Total Marks:</span>
                    <span class="value">{{ quiz.total_marks }}</span>
                </div>
                <div class="info-item">
                    <span class="label">Created:</span>
                    <span class="value">{{ quiz.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
            </div>

            <div class="info-card">
                <h3>Grading Thresholds</h3>
                <div class="grade-thresholds">
                    <div class="grade-item">
                        <span class="grade-badge grade-a">A</span>
                        <span class="threshold">≥ {{ quiz.grade_a_threshold }}%</span>
                    </div>
                    <div class="grade-item">
                        <span class="grade-badge grade-b">B</span>
                        <span class="threshold">≥ {{ quiz.grade_b_threshold }}%</span>
                    </div>
                    <div class="grade-item">
                        <span class="grade-badge grade-c">C</span>
                        <span class="threshold">≥ {{ quiz.grade_c_threshold }}%</span>
                    </div>
                    <div class="grade-item">
                        <span class="grade-badge grade-d">D</span>
                        <span class="threshold">≥ {{ quiz.grade_d_threshold }}%</span>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <h3>Quiz Statistics</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-value">{{ questions|length }}</span>
                        <span class="stat-label">Questions</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{{ total_attempts }}</span>
                        <span class="stat-label">Attempts</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">{{ "%.1f"|format(avg_score) }}%</span>
                        <span class="stat-label">Avg Score</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Questions Section -->
    <div class="questions-section">
        <div class="section-header">
            <h2>Questions ({{ questions|length }})</h2>
        </div>
        
        {% if questions %}
            <div class="questions-list">
                {% for question in questions %}
                <div class="question-card">
                    <div class="question-header">
                        <span class="question-number">Question {{ loop.index }}</span>
                        <span class="question-marks">{{ question.marks }} marks</span>
                    </div>
                    
                    <div class="question-content">
                        <p class="question-text">{{ question.question_text }}</p>
                        
                        {% if question.question_type == 'mcq' %}
                        <div class="mcq-options">
                            <div class="option-item {% if question.correct_answer == '1' %}correct-option{% endif %}">
                                <span class="option-marker">A</span>
                                <span class="option-text">{{ question.option1 }}</span>
                                {% if question.correct_answer == '1' %}
                                    <span class="correct-indicator">✓</span>
                                {% endif %}
                            </div>
                            <div class="option-item {% if question.correct_answer == '2' %}correct-option{% endif %}">
                                <span class="option-marker">B</span>
                                <span class="option-text">{{ question.option2 }}</span>
                                {% if question.correct_answer == '2' %}
                                    <span class="correct-indicator">✓</span>
                                {% endif %}
                            </div>
                            {% if question.option3 %}
                            <div class="option-item {% if question.correct_answer == '3' %}correct-option{% endif %}">
                                <span class="option-marker">C</span>
                                <span class="option-text">{{ question.option3 }}</span>
                                {% if question.correct_answer == '3' %}
                                    <span class="correct-indicator">✓</span>
                                {% endif %}
                            </div>
                            {% endif %}
                            {% if question.option4 %}
                            <div class="option-item {% if question.correct_answer == '4' %}correct-option{% endif %}">
                                <span class="option-marker">D</span>
                                <span class="option-text">{{ question.option4 }}</span>
                                {% if question.correct_answer == '4' %}
                                    <span class="correct-indicator">✓</span>
                                {% endif %}
                            </div>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-questions">
                <p>No questions have been added to this quiz yet.</p>
                <a href="{{ url_for('admin_edit_quiz', quiz_id=quiz.id) }}" class="btn btn-primary">Add Questions</a>
            </div>
        {% endif %}
    </div>

    <!-- Recent Attempts Section -->
    {% if recent_attempts %}
    <div class="recent-attempts-section">
        <div class="section-header">
            <h2>Recent Attempts</h2>
            <a href="{{ url_for('admin_quiz_attempts', quiz_filter=quiz.id) }}" class="btn btn-sm btn-outline">View All Attempts</a>
        </div>
        
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Student</th>
                        <th>Score</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attempt in recent_attempts %}
                    <tr>
                        <td>{{ attempt.student.name }}</td>
                        <td>
                            <span class="score-badge score-{{ 'excellent' if attempt.score >= 90 else 'good' if attempt.score >= 80 else 'average' if attempt.score >= 70 else 'below-average' if attempt.score >= 60 else 'poor' }}">
                                {{ "%.1f"|format(attempt.score) }}%
                            </span>
                        </td>
                        <td>{{ attempt.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                        <td>
                            <a href="{{ url_for('view_past_result', attempt_id=attempt.id) }}" class="btn btn-sm btn-info">View Details</a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Danger Zone -->
    <div class="danger-zone">
        <h3>Danger Zone</h3>
        <p>Permanently delete this quiz and all associated data. This action cannot be undone.</p>
        <button onclick="confirmDeleteQuiz({{ quiz.id }}, '{{ quiz.title }}')" class="btn btn-danger">Delete Quiz</button>
    </div>
</div>

{% block styles %}
{{ super() }}
<style>
.admin-view-quiz-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.admin-header h1 {
    color: #2c3e50;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.quiz-info-section {
    margin-bottom: 2rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.info-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
}

.info-card h3 {
    color: #495057;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.info-item .label {
    font-weight: 600;
    color: #6c757d;
}

.info-item .value {
    color: #495057;
}

.difficulty-badge, .grade-badge, .score-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.grade-a { background: #d4edda; color: #155724; }
.grade-b { background: #d1ecf1; color: #0c5460; }
.grade-c { background: #fff3cd; color: #856404; }
.grade-d { background: #f8d7da; color: #721c24; }

.score-excellent { background: #d4edda; color: #155724; }
.score-good { background: #d1ecf1; color: #0c5460; }
.score-average { background: #fff3cd; color: #856404; }
.score-below-average { background: #f8d7da; color: #721c24; }
.score-poor { background: #f5c6cb; color: #721c24; }

.grade-thresholds {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.grade-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
}

.questions-section, .recent-attempts-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.section-header h2 {
    color: #495057;
    margin: 0;
    font-size: 1.25rem;
}

.question-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e9ecef;
}

.question-number {
    font-weight: 600;
    color: #495057;
}

.question-marks {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.question-text {
    margin-bottom: 1rem;
    color: #495057;
    font-weight: 500;
}

.mcq-options {
    display: grid;
    gap: 0.5rem;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
    background: white;
    border: 1px solid #e9ecef;
}

.option-item.correct-option {
    background: #d4edda;
    border-color: #c3e6cb;
}

.option-marker {
    background: #6c757d;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.8rem;
    flex-shrink: 0;
}

.correct-option .option-marker {
    background: #28a745;
}

.option-text {
    flex: 1;
    color: #495057;
}

.correct-indicator {
    color: #28a745;
    font-weight: 600;
    font-size: 1.2rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

.danger-zone {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.danger-zone h3 {
    color: #721c24;
    margin-bottom: 0.5rem;
}

.danger-zone p {
    color: #721c24;
    margin-bottom: 1rem;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #117a8b;
}

.btn-outline {
    background: transparent;
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-outline:hover {
    background: #007bff;
    color: white;
}

.no-questions {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

@media (max-width: 768px) {
    .admin-view-quiz-container {
        padding: 1rem;
    }
    
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .grade-thresholds {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
function confirmDeleteQuiz(quizId, quizTitle) {
    if (confirm(`Are you sure you want to delete the quiz "${quizTitle}"? This action cannot be undone and will delete all associated attempts and data.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/quiz/delete/${quizId}`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}
{% endblock %}
