{% extends "base.html" %}

{% block title %}Sent Messages{% endblock %}

{% block content %}
<div class="container message-container">
    <div class="message-header-bar">
        <h1>Sent Messages</h1>
        <div class="message-actions">
            <a href="{{ url_for('compose_message') }}" class="btn btn-primary btn-sm">Compose</a>
            <a href="{{ url_for('inbox') }}" class="btn btn-outline-secondary btn-sm">Inbox</a>
        </div>
    </div>

    <ul class="list-group message-list">
        {% for message in messages %}
            <a href="{{ url_for('view_message', message_id=message.id) }}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1 sender">To: {{ message.receiver.name }}</h5>
                    <small class="timestamp">{{ message.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
                <p class="mb-1 subject">{{ message.subject | default('(No Subject)', true) }}</p>
            </a>
        {% else %}
            <li class="list-group-item no-messages">You haven't sent any messages.</li>
        {% endfor %}
    </ul>
     <div class="back-link-container">
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary btn-sm">Back to Dashboard</a>
    </div>
</div>

<!-- Reuse styles from inbox.html -->
<style>
.message-container {
    max-width: 900px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    padding: 0; /* Remove padding to allow header/list to span */
}

.message-header-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.message-header-bar h1 {
    margin: 0;
    font-size: 1.6rem;
}

.message-actions {
    display: flex;
    gap: 0.8rem;
}

.message-list {
    list-style: none;
    padding: 0;
    margin: 0;
    border-radius: 0 0 8px 8px; /* Match container radius */
}

/* Bootstrap-like List Group styles */
.list-group-item {
    position: relative;
    display: block;
    padding: 1rem 1.5rem;
    background-color: #fff;
    border-bottom: 1px solid rgba(0,0,0,.125);
    text-decoration: none;
    color: #212529;
}
.list-group-item:last-child {
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
    border-bottom: none;
}
.list-group-item:first-child {
    border-top-left-radius: inherit;
    border-top-right-radius: inherit;
}

.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
    z-index: 1;
    color: #495057;
    text-decoration: none;
    background-color: #f8f9fa;
}

.d-flex {
    display: flex;
}
.w-100 {
    width: 100%;
}
.justify-content-between {
    justify-content: space-between;
}
.mb-1 {
    margin-bottom: 0.25rem;
}

.sender {
    font-weight: 600; /* Bold sender/receiver */
    color: #0056b3;
}

.timestamp {
    font-size: 0.85em;
    color: #6c757d;
}

.subject {
    color: #495057;
    font-size: 0.95rem;
}

.no-messages {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
    background-color: #fff;
    border-bottom-right-radius: inherit;
    border-bottom-left-radius: inherit;
}

.back-link-container {
    text-align: center;
    padding: 1.5rem;
}

/* Basic Button Styles */
.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    border-radius: 0.25rem;
    transition: all .15s ease-in-out;
}
.btn-sm { /* Make buttons a bit smaller */
     padding: 0.375rem 0.75rem;
     font-size: 0.875rem;
}
.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}
.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #545b62;
}
.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
}
.btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

</style>
{% endblock %} 