{% extends "base.html" %}

{% block content %}
<div class="container">
    <div class="quiz-result-header">
        <h1>{{ quiz.title }}</h1>
        <div class="result-summary">
            <div class="score-box">
                <h2>Your Score</h2>
                <div class="score">{{ "%.2f"|format(attempt.score) }}%</div>
                <div class="grade">Grade: {{ grade }}</div>
            </div>
            <div class="stats-box">
                <div class="stat">
                    <span class="label">Correct Answers:</span>
                    <span class="value">{{ correct_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Incorrect Answers:</span>
                    <span class="value">{{ incorrect_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Omitted Questions:</span>
                    <span class="value">{{ omitted_count }}/{{ total_questions }}</span>
                </div>
                <div class="stat">
                    <span class="label">Time Taken:</span>
                    <span class="value">{{ time_taken }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="questions-review">
        <h2>Question Review</h2>
        {% for question, answer in questions_with_answers %}
        <div class="question-card {% if answer.is_correct %}correct{% elif answer.is_omitted|default(false) %}omitted{% elif question.question_text == '[This question has been removed]' %}removed{% else %}incorrect{% endif %}">
            <div class="question-header">
                <span class="question-number">Question {{ loop.index }}</span>
                <span class="question-status">
                    {% if answer.is_correct %}
                    ✓ Correct
                    {% elif answer.is_omitted|default(false) %}
                    ⚠ Omitted
                    {% elif question.question_text == '[This question has been removed]' %}
                    ℹ Modified Quiz
                    {% else %}
                    ✗ Incorrect
                    {% endif %}
                </span>
            </div>
            <div class="question-text">{{ question.question_text }}</div>
            <div class="answer-section">
                <div class="your-answer">
                    <strong>Your Answer:</strong>
                    {% if question.question_type == 'mcq' %}
                        {% if answer.selected_answer == '1' %}
                            {{ question.option1 }}
                        {% elif answer.selected_answer == '2' %}
                            {{ question.option2 }}
                        {% elif answer.selected_answer == '3' %}
                            {{ question.option3 }}
                        {% elif answer.selected_answer == '4' %}
                            {{ question.option4 }}
                        {% else %}
                            Not answered
                        {% endif %}
                    {% else %}
                        {{ answer.selected_answer }}
                    {% endif %}
                </div>
                {% if not answer.is_correct %}
                <div class="correct-answer">
                    <strong>Correct Answer:</strong>
                    {% if question.question_type == 'mcq' %}
                        {% if question.correct_answer == '1' %}
                            {{ question.option1 }}
                        {% elif question.correct_answer == '2' %}
                            {{ question.option2 }}
                        {% elif question.correct_answer == '3' %}
                            {{ question.option3 }}
                        {% elif question.correct_answer == '4' %}
                            {{ question.option4 }}
                        {% endif %}
                    {% else %}
                        {{ question.correct_answer }}
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="back-button">
        <a href="{{ url_for('student_dashboard') }}" class="btn">Back to Dashboard</a>
    </div>
</div>

<style>
.quiz-result-header {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.result-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.score-box {
    text-align: center;
}

.score {
    font-size: 3rem;
    font-weight: bold;
    color: #4CAF50;
}

.grade {
    font-size: 1.5rem;
    color: #666;
}

.stats-box {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat .label {
    display: block;
    color: #666;
    font-size: 0.9rem;
}

.stat .value {
    font-size: 1.2rem;
    font-weight: bold;
}

.questions-review {
    background: #fff;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.question-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.question-card.correct {
    border-left: 4px solid #4CAF50;
}

.question-card.incorrect {
    border-left: 4px solid #f44336;
}

.question-card.omitted {
    border-left: 4px solid #ff9800;
}

.question-card.removed {
    border-left: 4px solid #2196F3;
    background-color: #f0f8ff;
}

.question-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.question-status {
    font-weight: bold;
}

.question-text {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.answer-section {
    background: #f5f5f5;
    padding: 1rem;
    border-radius: 5px;
}

.correct-answer {
    color: #4CAF50;
    margin-top: 0.5rem;
}

.back-button {
    text-align: center;
    margin-top: 2rem;
}

.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    background-color: #4CAF50;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #45a049;
}
</style>
{% endblock %}