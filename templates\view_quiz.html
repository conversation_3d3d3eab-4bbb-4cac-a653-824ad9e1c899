{% extends "base.html" %}

{% block title %}View Quiz: {{ quiz.title }}{% endblock %}

{% block content %}
<div class="container view-quiz-container">
    <div class="quiz-header">
        <h1>{{ quiz.title }}</h1>
        <a href="{{ url_for('my_quizzes') }}" class="btn btn-secondary">Back to My Quizzes</a>
    </div>

    <div class="quiz-meta">
        <span class="meta-item"><strong>Difficulty:</strong> <span class="difficulty-badge {{ quiz.difficulty }}">{{ quiz.difficulty }}</span></span>
        <span class="meta-item"><strong>Time Limit:</strong> {{ quiz.time_limit }} minutes</span>
        <span class="meta-item"><strong>Total Marks:</strong> {{ quiz.total_marks }}</span>
    </div>
    {% if quiz.description %}
        <div class="quiz-description-view">
            <strong>Description:</strong>
            <p>{{ quiz.description }}</p>
        </div>
    {% endif %}

    <div class="questions-view-section">
        <h2>Questions</h2>
        {% for question in questions %}
            <div class="question-view-card">
                <div class="question-view-header">
                    <span class="question-view-number">Question {{ loop.index }}</span>
                    <span class="question-view-marks">({{ question.marks }} Marks)</span>
                </div>
                <p class="question-view-text">{{ question.question_text }}</p>
                
                <!-- Assuming MCQ type -->
                <div class="options-view">
                    <p><strong>Options:</strong></p>
                    <ul>
                        <li {% if question.correct_answer == '1' %}class="correct-answer"{% endif %}>A. {{ question.option1 }}</li>
                        <li {% if question.correct_answer == '2' %}class="correct-answer"{% endif %}>B. {{ question.option2 }}</li>
                        {% if question.option3 %}
                            <li {% if question.correct_answer == '3' %}class="correct-answer"{% endif %}>C. {{ question.option3 }}</li>
                        {% endif %}
                        {% if question.option4 %}
                            <li {% if question.correct_answer == '4' %}class="correct-answer"{% endif %}>D. {{ question.option4 }}</li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        {% else %}
            <p>No questions found for this quiz.</p>
        {% endfor %}
    </div>
</div>

<style>
.view-quiz-container {
    max-width: 900px;
    margin: 2rem auto;
}

.quiz-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.quiz-header h1 {
    margin: 0;
    color: #343a40;
}

.quiz-meta {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 5px;
    font-size: 0.95rem;
}

.meta-item strong {
    color: #495057;
}

.difficulty-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 10px;
    font-weight: bold;
    font-size: 0.85rem;
    text-transform: capitalize;
}
.difficulty-badge.easy { background-color: #d4edda; color: #155724; }
.difficulty-badge.medium { background-color: #fff3cd; color: #856404; }
.difficulty-badge.hard { background-color: #f8d7da; color: #721c24; }

.quiz-description-view {
    margin-bottom: 2rem;
    background-color: #fff;
    padding: 1.5rem;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}
.quiz-description-view p {
    margin-top: 0.5rem;
    color: #6c757d;
    line-height: 1.6;
}

.questions-view-section h2 {
    margin-bottom: 1.5rem;
    color: #007bff;
}

.question-view-card {
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-left: 4px solid #17a2b8; /* Different accent color */
    border-radius: 8px;
    padding: 1.5rem 2rem;
    margin-bottom: 1.5rem;
}

.question-view-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 0.8rem;
}

.question-view-number {
    font-weight: bold;
    font-size: 1.1em;
    color: #17a2b8;
}

.question-view-marks {
    font-size: 0.9em;
    color: #6c757d;
}

.question-view-text {
    margin-bottom: 1.2rem;
    font-size: 1.1em;
    color: #212529;
}

.options-view ul {
    list-style: none;
    padding-left: 1.5rem;
    margin-top: 0.5rem;
}

.options-view li {
    padding: 0.3rem 0;
    color: #495057;
}

.options-view li.correct-answer {
    font-weight: bold;
    color: #28a745; /* Green for correct answer */
}
.options-view li.correct-answer::before {
    content: "✓ "; /* Checkmark for correct answer */
    color: #28a745;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #5a6268;
}
</style>

{% endblock %} 