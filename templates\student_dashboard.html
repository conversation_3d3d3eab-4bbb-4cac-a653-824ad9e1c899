{% extends "base.html" %}

{% block title %}Student Dashboard{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Dashboard Common Styles (Apply consistently) */
.page-content.dashboard {
    background-color: #f8f9fa;
    padding: 2rem 0;
    flex-grow: 1;
}
.dashboard-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1rem;
}
.welcome-message {
    margin-bottom: 1.5rem;
    font-size: 1.2em;
    color: #555;
}
.dashboard-card {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}
.dashboard-card h2, .dashboard-card h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #343a40;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.8rem;
}
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

/* Student Dashboard Specific Styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem; /* Space between stats and graph */
}
.stat-item {
    background-color: #f1f3f5; /* Slightly different background */
    padding: 1.5rem;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #dee2e6;
}
.stat-item h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1em;
    color: #495057;
    font-weight: 500;
    border-bottom: none; /* Remove border for stat title */
    padding-bottom: 0;
}
.stat-value {
    font-size: 2.2em;
    font-weight: 600;
    color: dodgerblue; /* Use consistent color */
}

.progress-graph-container {
    position: relative;
    height: 300px; /* Or adjust as needed */
    width: 100%;
    padding: 1rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.filter-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-container label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.filter-container select {
    padding: 0.5rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background-color: white;
    font-size: 0.9rem;
    color: #333;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-container select:hover {
    border-color: #007bff;
}

.filter-container select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Responsive design for mobile */
@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .filter-container {
        width: 100%;
        justify-content: space-between;
    }

    .filter-container select {
        flex: 1;
        max-width: 200px;
    }
}

.quiz-card, .result-card { /* Style for quiz/result cards within the grid */
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid #e9ecef;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
}

.quiz-card:hover, .result-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.quiz-card h3, .result-card h3 {
    margin-top: 0;
    margin-bottom: 0.8rem;
    font-size: 1.2em;
    color: #343a40;
    border-bottom: none;
    padding-bottom: 0;
}

.quiz-description {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 1rem;
    flex-grow: 1; /* Make description take available space */
    min-height: 40px;
}

.quiz-details, .result-details {
    font-size: 0.85em;
    color: #495057;
    margin-bottom: 1.2rem;
    border-top: 1px solid #eee;
    padding-top: 1rem;
    margin-top: auto; /* Push details and button down */
}

.quiz-details span, .result-details div {
    display: block; /* Stack details */
    margin-bottom: 0.4rem;
}

.difficulty {
    display: inline-block; /* Make difficulty inline */
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-weight: 500;
    margin-right: 0.5rem;
}
/* Difficulty Colors */
.difficulty.easy { background: #d4edda; color: #155724; border: 1px solid #c3e6cb;}
.difficulty.medium { background: #fff3cd; color: #856404; border: 1px solid #ffeeba;}
.difficulty.hard { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb;}

.time-limit {
    color: #6c757d;
}

.score {
    font-size: 1.3em;
    font-weight: 600;
    color: dodgerblue;
}

.grade {
    font-weight: 500;
}

.date {
    color: #6c757d;
    font-size: 0.8em;
}

.btn {
    display: inline-block;
    width: auto; /* Don't make buttons full width */
    padding: 0.7rem 1.5rem;
    background-color: dodgerblue;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: background-color 0.3s, box-shadow 0.3s;
    border: none;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    margin-top: auto; /* Align button to bottom */
}

.btn:hover {
    background-color: #0056b3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.no-quizzes, .no-results {
    text-align: center;
    color: #6c757d;
    padding: 3rem 1rem;
    background-color: #f8f9fa;
    border: 1px dashed #ced4da;
    border-radius: 8px;
}

</style>
{% endblock %}

{% block content %}
<main class="page-content dashboard">
    <div class="dashboard-container">
        <h1>Student Dashboard</h1>
        <p class="welcome-message">Welcome back, {{ session.user_name }}!</p>

        <!-- Progress Tracking Section -->
        <div class="dashboard-card progress-section">
            <h2>Progress Overview</h2>
            <div class="stats-grid"> {# Grid for stats #}
                <div class="stat-item">
                    <h3>Quizzes Taken</h3>
                    <div class="stat-value">{{ total_attempts }}</div>
                </div>
                <div class="stat-item">
                    <h3>Average Score</h3>
                    <div class="stat-value">{{ "%.2f"|format(average_score) }}%</div>
                </div>
                {# Add more stats if needed #}
            </div>
            <div class="progress-graph-container"> {# Container for graph #}
                <canvas id="progressChart"></canvas>
            </div>
        </div>

        <!-- Available Quizzes Section -->
        <div class="dashboard-card quizzes-section">
            <div class="section-header">
                <h2>Available Quizzes</h2>
                <div class="filter-container">
                    <label for="difficulty-filter">Filter by Difficulty:</label>
                    <select id="difficulty-filter" onchange="filterQuizzes()">
                        <option value="all" {% if current_difficulty_filter == 'all' %}selected{% endif %}>All Difficulties</option>
                        {% for difficulty in available_difficulties %}
                            <option value="{{ difficulty }}" {% if current_difficulty_filter == difficulty %}selected{% endif %}>
                                {{ difficulty|capitalize }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            {% if quizzes %}
                <div class="dashboard-grid"> {# Reusing dashboard-grid #}
                    {% for quiz in quizzes %}
                        <div class="quiz-card">
                            <h3>{{ quiz.title }}</h3>
                            <p class="quiz-description">{{ quiz.description }}</p>
                            <div class="quiz-details">
                                <span class="difficulty {{ quiz.difficulty|lower }}">{{ quiz.difficulty|capitalize }}</span>
                                <span class="time-limit">Time: {{ quiz.time_limit }} mins</span>
                            </div>
                            <a href="{{ url_for('attempt_quiz', quiz_id=quiz.id) }}" class="fancy">
                                <span class="top-key"></span>
                                <span class="text">Start Quiz</span>
                                <span class="bottom-key-1"></span>
                                <span class="bottom-key-2"></span>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-quizzes">
                    {% if current_difficulty_filter == 'all' %}
                        <p>No new quizzes available at the moment. You may have completed all available quizzes!</p>
                    {% else %}
                        <p>No {{ current_difficulty_filter }} difficulty quizzes available. Try selecting a different difficulty level.</p>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Past Results Section -->
        <div class="dashboard-card past-results-section">
            <h2>Past Results</h2>
            {% if past_attempts %}
                <div class="dashboard-grid"> {# Reusing dashboard-grid #}
                    {% for attempt in past_attempts %}
                        <div class="result-card">
                            <h3>{{ attempt.quiz.title }}</h3>
                            <div class="result-details">
                                <div class="score">Score: {{ "%.2f"|format(attempt.score) }}%</div>
                                <div class="grade">Grade: {{ attempt.grade }}</div>
                                <div class="date">Taken: {{ attempt.submitted_at.strftime('%b %d, %Y %I:%M %p') }}</div>
                            </div>
                            <a href="{{ url_for('view_past_result', attempt_id=attempt.id) }}" class="fancy">
                                <span class="top-key"></span>
                                <span class="text">View Details</span>
                                <span class="bottom-key-1"></span>
                                <span class="bottom-key-2"></span>
                            </a>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="no-results">
                    <p>You haven't completed any quizzes yet.</p>
                </div>
            {% endif %}
        </div>
    </div>
</main>
{% endblock %}

{% block scripts %}
{{ super() }}
{# Ensure Chart.js is included - ideally in base.html or loaded async #}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Function to handle difficulty filter change
function filterQuizzes() {
    const select = document.getElementById('difficulty-filter');
    const selectedDifficulty = select.value;

    // Construct the URL with the difficulty parameter
    const currentUrl = new URL(window.location);
    if (selectedDifficulty === 'all') {
        currentUrl.searchParams.delete('difficulty');
    } else {
        currentUrl.searchParams.set('difficulty', selectedDifficulty);
    }

    // Redirect to the filtered URL
    window.location.href = currentUrl.toString();
}

document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('progressChart')?.getContext('2d');
    if (ctx) {
        const progressData = {{ quiz_progress | tojson }};
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: progressData.labels,
                datasets: [{
                    label: 'Quiz Score (%)',
                    data: progressData.scores,
                    backgroundColor: 'rgba(30, 144, 255, 0.7)',
                    borderColor: 'rgba(30, 144, 255, 1)',
                    borderWidth: 1,
                    borderRadius: 5,
                    hoverBackgroundColor: 'rgba(30, 144, 255, 0.9)'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Score (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Quiz Attempts'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Score: ${context.raw}%`;
                            }
                        }
                    }
                }
            }
        });
    }
});
</script>
{% endblock %}