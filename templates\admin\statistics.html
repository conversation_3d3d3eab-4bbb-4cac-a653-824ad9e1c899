{% extends "base.html" %}

{% block title %}Admin - System Statistics{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1>System Statistics</h1>
        <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
    </div>

    <div class="admin-content">
        <!-- User Statistics -->
        <div class="card">
            <div class="card-header">
                <h2>User Statistics</h2>
            </div>
            <div class="card-body">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Admins</h3>
                        <div class="stat-value">{{ user_counts.admin }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Teachers</h3>
                        <div class="stat-value">{{ user_counts.teacher }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Students</h3>
                        <div class="stat-value">{{ user_counts.student }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Parents</h3>
                        <div class="stat-value">{{ user_counts.parent }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quiz Statistics -->
        <div class="card">
            <div class="card-header">
                <h2>Quiz Statistics</h2>
            </div>
            <div class="card-body">
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Quizzes</h3>
                        <div class="stat-value">{{ total_quizzes }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Questions</h3>
                        <div class="stat-value">{{ total_questions }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Total Attempts</h3>
                        <div class="stat-value">{{ total_attempts }}</div>
                    </div>
                    <div class="stat-card">
                        <h3>Average Score</h3>
                        <div class="stat-value">{{ "%.1f"|format(avg_score) }}%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
            <div class="card-header">
                <h2>Recent Activity</h2>
            </div>
            <div class="card-body">
                <div class="activity-section">
                    <h3>Recent Quiz Attempts</h3>
                    {% if recent_attempts %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Student</th>
                                        <th>Quiz</th>
                                        <th>Score</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for attempt in recent_attempts %}
                                    <tr>
                                        <td>{{ attempt.student.name }}</td>
                                        <td>{{ attempt.quiz.title }}</td>
                                        <td>{{ "%.1f"|format(attempt.score) }}%</td>
                                        <td>{{ attempt.submitted_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No recent quiz attempts.</p>
                    {% endif %}
                </div>

                <div class="activity-section">
                    <h3>Recent Messages</h3>
                    {% if recent_messages %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>From</th>
                                        <th>To</th>
                                        <th>Subject</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for message in recent_messages %}
                                    <tr>
                                        <td>{{ message.sender.name }}</td>
                                        <td>{{ message.receiver.name }}</td>
                                        <td>{{ message.subject or '(No Subject)' }}</td>
                                        <td>{{ message.timestamp.strftime('%Y-%m-%d %H:%M') }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">No recent messages.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
}

.card-body {
    padding: 1rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-card {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    text-align: center;
}

.stat-card h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    color: #6c757d;
}

.stat-value {
    font-size: 2rem;
    font-weight: 600;
    color: #2c3e50;
}

.activity-section {
    margin-bottom: 2rem;
}

.activity-section h3 {
    font-size: 1.1rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
}

.table th {
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}

.text-muted {
    color: #6c757d;
}
</style>
{% endblock %} 