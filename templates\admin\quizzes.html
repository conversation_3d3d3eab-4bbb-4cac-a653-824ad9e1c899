{% extends "base.html" %}

{% block title %}Admin - Quiz Management{% endblock %}

{% block content %}
<div class="container admin-container">
    <div class="admin-header">
        <h1>Quiz Management</h1>
        <div class="header-actions">
            <a href="{{ url_for('admin_create_quiz') }}" class="btn btn-primary">Create New Quiz</a>
            <a href="{{ url_for('admin_dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>

    <div class="admin-content">
        <div class="card">
            <div class="card-header">
                <h2>All Quizzes</h2>
            </div>
            <div class="card-body">
                {% if quizzes %}
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Teacher</th>
                                    <th>Difficulty</th>
                                    <th>Questions</th>
                                    <th>Total Marks</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for quiz in quizzes %}
                                <tr>
                                    <td>
                                        <div class="quiz-title">
                                            <strong>{{ quiz.title }}</strong>
                                            {% if quiz.description %}
                                                <div class="quiz-description">{{ quiz.description[:100] }}{% if quiz.description|length > 100 %}...{% endif %}</div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>{{ quiz.teacher.name }}</td>
                                    <td>
                                        <span class="difficulty-badge difficulty-{{ quiz.difficulty|lower }}">
                                            {{ quiz.difficulty|capitalize }}
                                        </span>
                                    </td>
                                    <td>{{ quiz.questions|length }}</td>
                                    <td>{{ quiz.total_marks }}</td>
                                    <td>{{ quiz.created_at.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{{ url_for('admin_view_quiz', quiz_id=quiz.id) }}" class="btn btn-sm btn-info" title="View Quiz">View</a>
                                            <a href="{{ url_for('admin_edit_quiz', quiz_id=quiz.id) }}" class="btn btn-sm btn-primary" title="Edit Quiz">Edit</a>
                                            <button onclick="confirmDeleteQuiz({{ quiz.id }}, '{{ quiz.title }}')" class="btn btn-sm btn-danger" title="Delete Quiz">Delete</button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No quizzes found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.admin-container {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.admin-header h1 {
    margin: 0;
    color: #2c3e50;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

.quiz-title strong {
    color: #495057;
    font-size: 1rem;
}

.quiz-description {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
    font-style: italic;
}

.difficulty-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.difficulty-easy { background: #d4edda; color: #155724; }
.difficulty-medium { background: #fff3cd; color: #856404; }
.difficulty-hard { background: #f8d7da; color: #721c24; }

.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.card-header {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.card-header h2 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
}

.card-body {
    padding: 1rem;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    border-bottom: 1px solid #eee;
}

.table th {
    text-align: left;
    font-weight: 600;
    color: #2c3e50;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.badge-easy { background: #d4edda; color: #155724; }
.badge-medium { background: #fff3cd; color: #856404; }
.badge-hard { background: #f8d7da; color: #721c24; }

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    border: none;
    transition: all 0.2s;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:hover {
    opacity: 0.9;
}
</style>

<script>
function confirmDeleteQuiz(quizId, quizTitle) {
    if (confirm(`Are you sure you want to delete the quiz "${quizTitle}"? This action cannot be undone and will delete all associated attempts and data.`)) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/admin/quiz/delete/${quizId}`;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %} 