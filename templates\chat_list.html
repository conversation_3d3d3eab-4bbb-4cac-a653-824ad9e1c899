{% extends "base.html" %}

{% block title %}Chat{% endblock %}

{% block content %}
<div class="container chat-container">
    <h1>Your Chats</h1>

    <div class="chat-sections">
        <!-- Existing Conversations -->
        <div class="chat-list-section">
            <h2>Conversations</h2>
            {% if conversations %}
                <ul class="conversation-list">
                    {% for conv in conversations %}
                        <li class="conversation-item">
                            <a href="{{ url_for('view_conversation', conversation_id=conv.conversation_id) }}">
                                <span class="participant-name">{{ conv.other_user_name }} ({{ conv.other_user_role.capitalize() }})</span>
                                <span class="last-message">{{ conv.last_message_preview | truncate(40) }}</span>
                                <span class="last-updated">{{ conv.last_updated.strftime('%Y-%m-%d %H:%M') }}</span>
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            {% else %}
                <p class="no-chats">You have no active conversations.</p>
            {% endif %}
        </div>

        <!-- Start New Conversation -->
        <div class="new-chat-section">
            <h2>Start New Chat</h2>
             {% if potential_chat_targets %}
                <ul class="potential-list">
                    {% if user_role == 'teacher' %}
                        <!-- Teacher sees list of students/parents -->
                        {% for target in potential_chat_targets %}
                             <li class="potential-item">
                                 <span class="partner-name">Parent of {{ target.student_name }} ({{ target.parent_name }})</span>
                                 <a href="{{ url_for('start_or_get_conversation', other_user_id=target.parent_id) }}" class="btn btn-sm btn-start-chat">Chat</a>
                            </li>
                        {% endfor %}
                    {% elif user_role == 'parent' %}
                         <!-- Parent sees list of teachers -->
                         {% for target in potential_chat_targets %}
                             <li class="potential-item">
                                 <span class="partner-name">{{ target.teacher_name }} (Teacher)</span>
                                 <a href="{{ url_for('start_or_get_conversation', other_user_id=target.teacher_id) }}" class="btn btn-sm btn-start-chat">Chat</a>
                            </li>
                        {% endfor %}
                    {% endif %}
                </ul>
            {% else %}
                 {% if user_role == 'teacher' %}
                    <p class="no-chats">No parents available to chat with (ensure students have linked parent accounts).</p>
                 {% elif user_role == 'parent' %}
                    <p class="no-chats">No teachers available to chat with.</p>
                 {% endif %}
            {% endif %}
        </div>
    </div>
     <a href="{{ url_for('dashboard') }}" class="btn btn-secondary back-btn">Back to Dashboard</a>
</div>

<style>
.chat-container {
    max-width: 1000px;
    margin: 2rem auto;
}

.chat-container h1 {
    text-align: center;
    margin-bottom: 2rem;
}

.chat-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.chat-list-section, .new-chat-section {
    background-color: #fff;
    padding: 1.5rem 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.07);
}

.chat-list-section h2, .new-chat-section h2 {
    border-bottom: 1px solid #eee;
    padding-bottom: 0.8rem;
    margin-bottom: 1rem;
    color: #007bff;
}

.conversation-list, .potential-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.conversation-item a {
    display: block;
    padding: 1rem 0.5rem;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: #333;
    transition: background-color 0.2s;
}

.conversation-item:last-child a {
    border-bottom: none;
}

.conversation-item a:hover {
    background-color: #f8f9fa;
}

.participant-name {
    font-weight: bold;
    display: block;
    margin-bottom: 0.2rem;
}

.last-message {
    font-size: 0.9rem;
    color: #6c757d;
    display: block;
}

.last-updated {
    font-size: 0.8rem;
    color: #adb5bd;
    float: right;
}

.potential-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0.5rem;
     border-bottom: 1px solid #f0f0f0;
}
.potential-item:last-child {
     border-bottom: none;
}

.partner-name {
     color: #495057;
}

.btn-sm {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
}

.btn-start-chat {
     background-color: #28a745;
     border-color: #28a745;
     color: white;
}
.btn-start-chat:hover {
    background-color: #218838;
     border-color: #1e7e34;
}

.no-chats {
    text-align: center;
    color: #6c757d;
    padding: 1rem;
}

.back-btn {
    display: block;
    width: fit-content;
    margin: 1rem auto 0 auto;
}

</style>
{% endblock %} 