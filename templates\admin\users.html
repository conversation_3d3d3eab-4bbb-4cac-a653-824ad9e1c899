{% extends "base.html" %}

{% block title %}Manage Users{% endblock %}

{% block content %}
<main class="page-content admin-panel">
    <div class="container">
        <h1>Manage Users</h1>
        <a href="{{ url_for('admin_dashboard') }}" class="back-link">&larr; Back to Admin Dashboard</a>

        {% with messages = get_flashed_messages(with_categories=true) %}
          {% if messages %}
            {% for category, message in messages %}
              <div class="flash-message {{ category }}">{{ message }}</div>
            {% endfor %}
          {% endif %}
        {% endwith %}

        <table class="user-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Parent Email</th>
                    <th>Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.id }}</td>
                    <td>{{ user.name }}</td>
                    <td>{{ user.email }}</td>
                    <td>{{ user.role }}</td>
                    <td>{{ user.parent_email if user.parent_email else 'N/A' }}</td>
                    <td>
                        {% if user.is_verified %}
                            <span class="status-verified">Verified</span>
                        {% else %}
                            <span class="status-pending">Pending</span>
                        {% endif %}
                    </td>
                    <td>
                        <a href="{{ url_for('admin_edit_user', user_id=user.id) }}" class="btn btn-edit">Edit</a>
                        <!-- Add Delete button - use a form for safety -->
                        <form action="{{ url_for('admin_delete_user', user_id=user.id) }}" method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user? This cannot be undone.');">
                            <button type="submit" class="btn btn-delete">Delete</button>
                        </form>
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td colspan="7">No users found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

    </div>
</main>

<style>
/* Inherits styles from admin_dashboard.html for .admin-panel */

.back-link {
    display: inline-block;
    margin-bottom: 1.5rem;
    color: #555;
    text-decoration: none;
}
.back-link:hover {
    text-decoration: underline;
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.user-table th, .user-table td {
    border: 1px solid #ddd;
    padding: 0.8rem 1rem;
    text-align: left;
    vertical-align: middle;
}

.user-table th {
    background-color: #f8f8f8;
    font-weight: 600;
    color: #333;
}

.user-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.user-table tbody tr:hover {
    background-color: #f1f1f1;
}

.btn {
    display: inline-block;
    padding: 0.4rem 0.8rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9em;
    margin-right: 5px;
    transition: background-color 0.2s ease;
}

.btn-edit {
    background-color: #ffc107; /* Yellow */
    color: #333;
}
.btn-edit:hover {
    background-color: #e0a800;
}

.btn-delete {
    background-color: #dc3545; /* Red */
    color: white;
}
.btn-delete:hover {
    background-color: #c82333;
}

/* Flash Messages (reuse from login/signup if available or add here) */
.flash-message {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    color: white;
}
.flash-message.success { background-color: #28a745; }
.flash-message.error { background-color: #dc3545; }
.flash-message.info { background-color: #17a2b8; }

.status-verified {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #28a745;
    color: white;
    border-radius: 4px;
    font-size: 0.85em;
}

.status-pending {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #ffc107;
    color: #333;
    border-radius: 4px;
    font-size: 0.85em;
}
</style>
{% endblock %}