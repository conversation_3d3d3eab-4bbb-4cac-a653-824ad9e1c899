{% extends "base.html" %}

{% block title %}Compose Message{% endblock %}

{% block content %}
<div class="container compose-container">
    <div class="card-header">
        <h1>Compose New Message</h1>
    </div>
    <div class="card-body">
        <form method="POST" action="{{ url_for('compose_message') }}">
            <div class="form-group">
                <label for="receiver_username">To (Username):</label>
                <input type="text" id="receiver_username" name="receiver_username" 
                       value="{{ recipient.name if recipient else '' }}" 
                       class="form-control {% if recipient %}is-readonly{% endif %}" 
                       required {% if recipient %}readonly{% endif %}>
                {% if recipient %}
                    <small class="form-text text-muted">Sending to {{ recipient.name }} ({{ recipient.role.capitalize() }})</small>
                {% endif %}
            </div>
            <div class="form-group">
                <label for="subject">Subject:</label>
                <input type="text" id="subject" name="subject" value="{{ subject if subject else '' }}" class="form-control">
            </div>
            <div class="form-group">
                <label for="body">Message:</label>
                <textarea id="body" name="body" rows="10" required class="form-control">{{ body if body else '' }}</textarea>
            </div>
            <div class="form-actions">
                <a href="{{ url_for('inbox') }}" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">Send Message</button>
            </div>
        </form>
    </div>
</div>

<style>
/* Shared styles can go in base.css if preferred */
.compose-container {
    max-width: 750px;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    overflow: hidden; /* Contain header background */
}

.card-header {
    background-color: #007bff;
    color: white;
    padding: 1rem 1.5rem;
}

.card-header h1 {
    margin: 0;
    font-size: 1.5rem;
}

.card-body {
    padding: 2rem 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-control:focus {
    color: #495057;
    background-color: #fff;
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-control.is-readonly {
    background-color: #e9ecef;
    opacity: 1;
    cursor: not-allowed;
}

textarea.form-control {
    resize: vertical;
    min-height: 150px;
}

.form-text.text-muted {
    font-size: 0.875em;
    color: #6c757d;
    margin-top: 0.25rem;
}

.form-actions {
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.8rem;
}

.btn {
    display: inline-block;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.6rem 1.2rem;
    font-size: 1rem;
    border-radius: 0.25rem;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}
.btn-primary:hover {
    color: #fff;
    background-color: #0069d9;
    border-color: #0062cc;
}

.btn-secondary {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}
.btn-secondary:hover {
    color: #fff;
    background-color: #5a6268;
    border-color: #545b62;
}
</style>
{% endblock %} 