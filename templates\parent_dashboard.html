{% extends "base.html" %}

{% block title %}Parent Dashboard{% endblock %}

{% block styles %}
{{ super() }}
<style>
/* Dashboard Common Styles */
.page-content.dashboard {
    background-color: #f8f9fa;
    padding: 2rem 0;
    flex-grow: 1;
}
.dashboard-container {
    max-width: 1100px;
    margin: 0 auto;
    padding: 0 1rem;
}
.welcome-message {
    margin-bottom: 1.5rem;
    font-size: 1.2em;
    color: #555;
}
.dashboard-card {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid #e9ecef;
}
.dashboard-card h2, .dashboard-card h3 {
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #343a40;
    font-weight: 600;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.8rem;
}

/* Parent Dashboard Specific Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-header h1 {
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 0.8rem;
}

.child-card h3 {
    /* Overwrite default card h3 border if needed or adjust */
    border-bottom: none;
    margin-bottom: 0.5rem;
}

.child-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem; /* Space below header */
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.performance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.performance-table th,
.performance-table td {
    border: 1px solid #e9ecef;
    padding: 0.8rem 1rem;
    text-align: left;
    vertical-align: middle;
    font-size: 0.95em;
}

.performance-table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.performance-table tbody tr:nth-child(even) {
    background-color: #fdfdfd;
}

.performance-table tbody tr:hover {
    background-color: #f1f3f5;
}

.btn {
    display: inline-block;
    padding: 0.6rem 1.2rem; /* Slightly smaller */
    background-color: dodgerblue;
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: background-color 0.3s, box-shadow 0.3s;
    border: none;
    cursor: pointer;
    font-weight: 500;
    text-align: center;
    font-size: 0.9em; /* Smaller font */
}

.btn:hover {
    background-color: #0056b3;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid dodgerblue;
    color: dodgerblue;
}
.btn-outline:hover {
    background-color: rgba(30, 144, 255, 0.1);
    color: #0056b3;
    border-color: #0056b3;
    box-shadow: none;
}

.btn-info {
    background-color: #17a2b8;
    border-color: #17a2b8;
}
.btn-info:hover {
    background-color: #138496;
    border-color: #117a8b;
}

.no-children, .no-attempts {
    text-align: center;
    color: #6c757d;
    padding: 3rem 1rem;
    background-color: #f8f9fa;
    border: 1px dashed #ced4da;
    border-radius: 8px;
}

</style>
{% endblock %}

{% block content %}
<main class="page-content dashboard">
    <div class="dashboard-container">
        <div class="page-header">
            <h1>Parent Dashboard</h1>
            <div class="header-actions">
                <a href="{{ url_for('inbox') }}" class="fancy">
                    <span class="top-key"></span>
                    <span class="text">Open Inbox</span>
                    <span class="bottom-key-1"></span>
                    <span class="bottom-key-2"></span>
                </a>
            </div>
        </div>
        <p class="welcome-message">Welcome, {{ session.user_name }}!</p>

        <h2>Your Children's Performance</h2>

        {% if children_data %}
            {% for child in children_data %}
            <div class="dashboard-card child-card"> {# Use dashboard-card #}
                <div class="child-card-header">
                     <h3>{{ child.name }}</h3>
                     <a href="{{ url_for('view_report_card', child_id=child.id) }}" class="fancy">
                        <span class="top-key"></span>
                        <span class="text">View Report Card</span>
                        <span class="bottom-key-1"></span>
                        <span class="bottom-key-2"></span>
                     </a>
                </div>
                
                <h4>Recent Quiz Attempts</h4>
                {% if child.attempts %}
                <div class="table-responsive"> {# Add responsive wrapper for table #}
                    <table class="performance-table">
                        <thead>
                            <tr>
                                <th>Quiz Title</th>
                                <th>Score</th>
                                <th>Grade</th>
                                <th>Date Taken</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for attempt in child.attempts %}
                            <tr>
                                <td>{{ attempt.quiz.title }}</td>
                                <td>{{ "%.2f"|format(attempt.score) }}%</td>
                                <td>{{ attempt.grade }}</td>
                                <td>{{ attempt.submitted_at.strftime('%b %d, %Y %I:%M %p') }}</td>
                                <td>
                                    <a href="{{ url_for('view_child_result', attempt_id=attempt.id) }}" class="fancy">
                                        <span class="top-key"></span>
                                        <span class="text">View Details</span>
                                        <span class="bottom-key-1"></span>
                                        <span class="bottom-key-2"></span>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="no-attempts">{{ child.name }} has not attempted any quizzes yet.</p>
                {% endif %}
            </div>
            {% endfor %}
        {% else %}
        <div class="no-children">
            <p>We couldn't find any student accounts linked to your parent email.</p>
            <p><small>If your child has registered, ensure they used your email ({{ session.email }}) correctly.</small></p>
        </div>
        {% endif %}
    </div>
</main>
{% endblock %}